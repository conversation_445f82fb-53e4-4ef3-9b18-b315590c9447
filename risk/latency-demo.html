<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>响应耗时风险检测 - 演示</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2.5rem;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .demo-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #3498db;
        }
        
        .demo-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .scenario-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin-bottom: 20px;
        }
        
        button {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }
        
        .btn-primary { background: #3498db; color: white; }
        .btn-success { background: #2ecc71; color: white; }
        .btn-warning { background: #f39c12; color: white; }
        .btn-danger { background: #e74c3c; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .result-panel {
            background: #fff;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .risk-display {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .risk-value {
            font-size: 3rem;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .risk-low { color: #2ecc71; }
        .risk-medium { color: #f39c12; }
        .risk-high { color: #e74c3c; }
        
        .percentile-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .percentile-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid #3498db;
        }
        
        .percentile-name {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .percentile-value {
            font-size: 1.2rem;
            color: #3498db;
            margin-bottom: 3px;
        }
        
        .percentile-risk {
            font-size: 0.8rem;
            color: #666;
        }
        
        .alerts-section {
            margin-top: 20px;
        }
        
        .alert {
            padding: 12px 15px;
            border-radius: 6px;
            margin-bottom: 10px;
            border-left: 4px solid;
        }
        
        .alert-critical {
            background: #ffebee;
            color: #c62828;
            border-left-color: #c62828;
        }
        
        .alert-warning {
            background: #fff8e1;
            color: #f57c00;
            border-left-color: #f57c00;
        }
        
        .alert-info {
            background: #e3f2fd;
            color: #1565c0;
            border-left-color: #1565c0;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .view-toggle-btn {
            padding: 6px 12px;
            border: 1px solid #dee2e6;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.3s ease;
        }

        .view-toggle-btn:hover {
            border-color: #3498db;
            color: #3498db;
        }

        .view-toggle-btn.active {
            background: #3498db;
            border-color: #3498db;
            color: white;
        }

        @media (max-width: 768px) {
            .demo-grid {
                grid-template-columns: 1fr;
            }
            .scenario-buttons {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 响应耗时风险检测算法演示</h1>
        
        <div class="demo-grid">
            <div class="demo-section">
                <h3>📊 测试场景</h3>
                <p>选择不同的测试场景来体验算法的检测能力：</p>
                <div class="scenario-buttons">
                    <button class="btn-success" onclick="runScenario('normal')">正常稳定</button>
                    <button class="btn-warning" onclick="runScenario('spike')">P99尖刺</button>
                    <button class="btn-danger" onclick="runScenario('degradation')">系统劣化</button>
                    <button class="btn-warning" onclick="runScenario('tail')">长尾恶化</button>
                    <button class="btn-info" onclick="runScenario('peak')">高峰模式</button>
                    <button class="btn-success" onclick="runScenario('improvement')">性能改善</button>
                    <button class="btn-warning" onclick="runScenario('external')">外网依赖</button>
                </div>
            </div>
            
            <div class="demo-section">
                <h3>🎯 算法特点</h3>
                <ul style="line-height: 1.8;">
                    <li><strong>单向异常检测</strong> - 只有耗时增加才是异常</li>
                    <li><strong>多分位数分析</strong> - 全面评估P50/P75/P95/P99</li>
                    <li><strong>同比模式学习</strong> - 自动适应业务高峰</li>
                    <li><strong>智能告警策略</strong> - 减少误报提高精度</li>
                    <li><strong>业务影响评估</strong> - 从用户体验角度分析</li>
                </ul>

                <div style="margin-top: 15px;">
                    <h4>📊 数据对比视图</h4>
                    <div style="display: flex; gap: 10px; margin-top: 10px;">
                        <button class="view-toggle-btn active" data-view="current">当前数据</button>
                        <button class="view-toggle-btn" data-view="comparison">对比视图</button>
                        <button class="view-toggle-btn" data-view="difference">差值分析</button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="result-panel">
            <div id="loading" class="loading">
                点击上方按钮开始测试...
            </div>
            
            <div id="results" style="display: none;">
                <div class="risk-display">
                    <h3>综合风险评估</h3>
                    <div id="riskValue" class="risk-value">-</div>
                    <div id="riskLevel">-</div>
                    <div>置信度: <span id="confidence">-</span></div>
                </div>
                
                <h4>各分位数详情</h4>
                <div id="percentileGrid" class="percentile-grid">
                    <!-- 分位数卡片将在这里生成 -->
                </div>
                
                <div class="alerts-section">
                    <h4>智能告警</h4>
                    <div id="alertsContainer">
                        <!-- 告警信息将在这里显示 -->
                    </div>
                </div>
                
                <div style="margin-top: 20px;">
                    <h4>业务影响评估</h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px;">
                        <div style="text-align: center; padding: 10px; background: #f8f9fa; border-radius: 6px;">
                            <div style="font-weight: bold;">用户体验</div>
                            <div id="userExperience">-</div>
                        </div>
                        <div style="text-align: center; padding: 10px; background: #f8f9fa; border-radius: 6px;">
                            <div style="font-weight: bold;">系统稳定性</div>
                            <div id="systemStability">-</div>
                        </div>
                        <div style="text-align: center; padding: 10px; background: #f8f9fa; border-radius: 6px;">
                            <div style="font-weight: bold;">业务连续性</div>
                            <div id="businessContinuity">-</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="latency-risk-detector.js"></script>
    <script>
        // 全局变量
        let currentScenario = null;
        let currentViewMode = 'current';

        // ========== 数据压缩/解压缩功能 ==========

        function ultraDecompressLatencyData(compressedStr) {
            if (!compressedStr) return [];

            try {
                const parts = compressedStr.split('|');
                if (parts.length < 3) return [];

                const baseTime = parseInt(parts[0], 36);
                const interval = parseInt(parts[1], 36);
                const dataStr = parts[2];

                const dataGroups = dataStr.split(',');
                const categories = ['1', '0.5', '0.75', '0.95', '0.99'];
                const result = [];

                dataGroups.forEach((group, timeIndex) => {
                    if (!group) return;

                    const values = group.split('.');
                    if (values.length !== 5) return;

                    const time = baseTime + timeIndex * interval;

                    values.forEach((valueStr, catIndex) => {
                        const quantizedValue = parseInt(valueStr, 36);
                        if (isNaN(quantizedValue)) return;

                        const originalValue = quantizedValue / 6553.5;

                        result.push({
                            time: time,
                            value: originalValue.toFixed(6),
                            category: categories[catIndex]
                        });
                    });
                });

                return result;

            } catch (error) {
                console.error('解压缩失败:', error);
                return [];
            }
        }

        // ========== URL参数处理 ==========

        function parseUrlParams() {
            const params = new URLSearchParams(window.location.search);

            // 检查压缩数据参数
            if (params.has('data')) {
                const currentCompressed = params.get('data');
                const yoyCompressed = params.get('yoy') || currentCompressed;

                try {
                    const currentData = ultraDecompressLatencyData(currentCompressed);
                    const yoyData = ultraDecompressLatencyData(yoyCompressed);

                    if (currentData.length > 0 && yoyData.length > 0) {
                        return {
                            type: 'compressed',
                            currentData: currentData,
                            yoyData: yoyData,
                            viewMode: params.get('view') || 'current'
                        };
                    }
                } catch (error) {
                    console.error('解析压缩数据失败:', error);
                }
            }

            // 检查场景参数
            if (params.has('scenario')) {
                return {
                    type: 'scenario',
                    scenario: params.get('scenario'),
                    viewMode: params.get('view') || 'current'
                };
            }

            return null;
        }

        // 测试场景数据
        const scenarios = {
            normal: {
                name: "正常稳定场景",
                description: "各分位数稳定运行，无异常波动",
                current: generateNormalData(),
                yoy: generateNormalData()
            },
            spike: {
                name: "P99突发尖刺场景", 
                description: "P99在短时间内大幅上升",
                current: generateSpikeData(),
                yoy: generateNormalData()
            },
            degradation: {
                name: "系统性劣化场景",
                description: "所有分位数同步上升",
                current: generateDegradationData(),
                yoy: generateNormalData()
            },
            tail: {
                name: "长尾恶化场景",
                description: "P99上升但平均值稳定",
                current: generateTailData(),
                yoy: generateNormalData()
            },
            peak: {
                name: "高峰时段模式",
                description: "业务高峰期的正常模式",
                current: generatePeakData(2.0),
                yoy: generatePeakData(1.8)
            },
            improvement: {
                name: "性能改善场景",
                description: "各分位数下降，性能优化",
                current: generateImprovementData(),
                yoy: generateNormalData()
            },
            external: {
                name: "外网依赖场景",
                description: "P99/P95上升但P50/P75稳定",
                current: generateExternalDependencyData(),
                yoy: generateExternalDependencyData(0.8) // 同比也有类似模式但程度稍轻
            }
        };

        // 数据生成函数
        function generateNormalData() {
            const data = [];
            const baseTime = 1757179920;
            const baseLatencies = { '1': 0.02, '0.5': 0.015, '0.75': 0.025, '0.95': 0.05, '0.99': 0.1 };
            
            for (let i = 0; i < 30; i++) {
                const time = baseTime + i * 60;
                Object.entries(baseLatencies).forEach(([category, baseValue]) => {
                    const noise = baseValue * (1 + (Math.random() - 0.5) * 0.1);
                    data.push({
                        time: time,
                        value: Math.max(0.001, noise).toFixed(6),
                        category: category
                    });
                });
            }
            return data;
        }

        function generateSpikeData() {
            const data = [];
            const baseTime = 1757179920;
            const baseLatencies = { '1': 0.02, '0.5': 0.015, '0.75': 0.025, '0.95': 0.05, '0.99': 0.1 };
            
            for (let i = 0; i < 30; i++) {
                const time = baseTime + i * 60;
                const isSpike = i >= 20 && i <= 25;
                
                Object.entries(baseLatencies).forEach(([category, baseValue]) => {
                    let value = baseValue;
                    if (isSpike) {
                        const multiplier = category === '0.99' ? 8 :
                                         category === '0.95' ? 5 :
                                         category === '0.75' ? 2 : 1.2;
                        value = baseValue * multiplier;
                    }
                    
                    data.push({
                        time: time,
                        value: Math.max(0.001, value).toFixed(6),
                        category: category
                    });
                });
            }
            return data;
        }

        function generateDegradationData() {
            const data = [];
            const baseTime = 1757179920;
            const baseLatencies = { '1': 0.02, '0.5': 0.015, '0.75': 0.025, '0.95': 0.05, '0.99': 0.1 };
            
            for (let i = 0; i < 30; i++) {
                const time = baseTime + i * 60;
                const factor = 1 + 2 * (i / 30); // 渐进式增长到3倍
                
                Object.entries(baseLatencies).forEach(([category, baseValue]) => {
                    const value = baseValue * factor;
                    data.push({
                        time: time,
                        value: Math.max(0.001, value).toFixed(6),
                        category: category
                    });
                });
            }
            return data;
        }

        function generateTailData() {
            const data = [];
            const baseTime = 1757179920;
            const baseLatencies = { '1': 0.02, '0.5': 0.015, '0.75': 0.025, '0.95': 0.05, '0.99': 0.1 };
            
            for (let i = 0; i < 30; i++) {
                const time = baseTime + i * 60;
                
                Object.entries(baseLatencies).forEach(([category, baseValue]) => {
                    let value = baseValue;
                    if (category === '0.99') {
                        value = baseValue * 4; // P99增长4倍
                    } else if (category === '0.95') {
                        value = baseValue * 2; // P95增长2倍
                    }
                    
                    data.push({
                        time: time,
                        value: Math.max(0.001, value).toFixed(6),
                        category: category
                    });
                });
            }
            return data;
        }

        function generatePeakData(multiplier) {
            const data = [];
            const baseTime = 1757179920;
            const baseLatencies = { '1': 0.02, '0.5': 0.015, '0.75': 0.025, '0.95': 0.05, '0.99': 0.1 };
            
            for (let i = 0; i < 30; i++) {
                const time = baseTime + i * 60;
                const isPeak = i >= 9 && i <= 21; // 高峰时段
                
                Object.entries(baseLatencies).forEach(([category, baseValue]) => {
                    let value = baseValue;
                    if (isPeak) {
                        const peakMultiplier = category === '0.99' ? multiplier :
                                             category === '0.95' ? multiplier * 0.8 :
                                             category === '0.75' ? multiplier * 0.5 :
                                             multiplier * 0.3;
                        value = baseValue * peakMultiplier;
                    }
                    
                    data.push({
                        time: time,
                        value: Math.max(0.001, value).toFixed(6),
                        category: category
                    });
                });
            }
            return data;
        }

        function generateImprovementData() {
            const data = [];
            const baseTime = 1757179920;
            const baseLatencies = { '1': 0.02, '0.5': 0.015, '0.75': 0.025, '0.95': 0.05, '0.99': 0.1 };

            for (let i = 0; i < 30; i++) {
                const time = baseTime + i * 60;
                const factor = 1 - 0.4 * (i / 30); // 渐进式改善到60%

                Object.entries(baseLatencies).forEach(([category, baseValue]) => {
                    const value = baseValue * factor;
                    data.push({
                        time: time,
                        value: Math.max(0.001, value).toFixed(6),
                        category: category
                    });
                });
            }
            return data;
        }

        function generateExternalDependencyData(intensityFactor = 1.0) {
            const data = [];
            const baseTime = 1757179920;
            const baseLatencies = { '1': 0.02, '0.5': 0.015, '0.75': 0.025, '0.95': 0.05, '0.99': 0.1 };

            for (let i = 0; i < 30; i++) {
                const time = baseTime + i * 60;

                Object.entries(baseLatencies).forEach(([category, baseValue]) => {
                    let value = baseValue;

                    // 外网依赖影响：主要影响高分位数
                    if (category === '0.99') {
                        value = baseValue * (5 * intensityFactor); // P99上升5倍
                    } else if (category === '0.95') {
                        value = baseValue * (3 * intensityFactor); // P95上升3倍
                    } else if (category === '0.75') {
                        value = baseValue * (1.2 * intensityFactor); // P75轻微影响
                    }
                    // P50和AVG基本不受影响

                    // 添加一些随机噪声
                    const noise = 1 + (Math.random() - 0.5) * 0.1;
                    value *= noise;

                    data.push({
                        time: time,
                        value: Math.max(0.001, value).toFixed(6),
                        category: category
                    });
                });
            }
            return data;
        }

        // 运行测试场景
        function runScenario(scenarioName) {
            const scenario = scenarios[scenarioName];
            if (!scenario) return;

            // 显示加载状态
            document.getElementById('loading').style.display = 'block';
            document.getElementById('results').style.display = 'none';

            // 模拟分析过程
            setTimeout(() => {
                try {
                    const result = detectLatencyRiskFromWindows(scenario.current, scenario.yoy, {
                        serviceType: 'MICROSERVICE',
                        enablePatternLearning: true
                    });

                    displayResults(result, scenario);
                } catch (error) {
                    console.error('分析错误:', error);
                    document.getElementById('loading').innerHTML = `
                        <div style="color: #e74c3c;">
                            <h4>分析出错</h4>
                            <p>${error.message}</p>
                        </div>
                    `;
                }
            }, 500);
        }

        // 显示结果
        function displayResults(result, scenario) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('results').style.display = 'block';

            // 更新风险值
            const riskPercent = (result.risk * 100).toFixed(1) + '%';
            document.getElementById('riskValue').textContent = riskPercent;

            // 设置风险级别
            const riskValue = document.getElementById('riskValue');
            const riskLevel = document.getElementById('riskLevel');
            
            if (result.risk > 0.7) {
                riskValue.className = 'risk-value risk-high';
                riskLevel.textContent = '高风险 - 需要立即关注';
            } else if (result.risk > 0.4) {
                riskValue.className = 'risk-value risk-medium';
                riskLevel.textContent = '中风险 - 建议监控';
            } else {
                riskValue.className = 'risk-value risk-low';
                riskLevel.textContent = '低风险 - 运行正常';
            }

            // 更新置信度
            document.getElementById('confidence').textContent = (result.confidence * 100).toFixed(1) + '%';

            // 更新分位数信息
            updatePercentileCards(result);

            // 更新告警信息
            updateAlerts(result);

            // 更新业务影响
            if (result.businessImpact) {
                document.getElementById('userExperience').textContent = result.businessImpact.userExperience.level;
                document.getElementById('systemStability').textContent = result.businessImpact.systemStability.level;
                document.getElementById('businessContinuity').textContent = result.businessImpact.businessContinuity.level;
            }
        }

        function updatePercentileCards(result) {
            const container = document.getElementById('percentileGrid');
            const percentileNames = {
                '0.99': 'P99', '0.95': 'P95', '0.75': 'P75', '0.5': 'P50', '1': 'AVG'
            };

            let html = '';
            Object.entries(result.percentileRisks || {}).forEach(([percentile, risk]) => {
                const name = percentileNames[percentile] || percentile;
                const value = (risk.currentValue * 1000).toFixed(1) + 'ms';
                const riskPercent = (risk.combinedRisk * 100).toFixed(1) + '%';
                
                html += `
                    <div class="percentile-card">
                        <div class="percentile-name">${name}</div>
                        <div class="percentile-value">${value}</div>
                        <div class="percentile-risk">风险: ${riskPercent}</div>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        function updateAlerts(result) {
            const container = document.getElementById('alertsContainer');
            
            if (!result.alerts || result.alerts.length === 0) {
                container.innerHTML = '<div style="color: #2ecc71; text-align: center; padding: 15px;">✅ 无告警，系统运行正常</div>';
                return;
            }

            let html = '';
            result.alerts.forEach(alert => {
                const alertClass = alert.level === 'CRITICAL' ? 'alert-critical' :
                                 alert.level === 'WARNING' ? 'alert-warning' : 'alert-info';
                
                html += `
                    <div class="alert ${alertClass}">
                        <strong>[${alert.level}]</strong> ${alert.message}
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        // 视图切换功能
        function initViewToggle() {
            document.querySelectorAll('.view-toggle-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    // 更新按钮状态
                    document.querySelectorAll('.view-toggle-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');

                    // 切换视图
                    currentViewMode = this.getAttribute('data-view');

                    // 如果有当前场景，重新显示结果
                    if (currentScenario) {
                        displayResultsWithView(currentScenario.result, currentScenario.scenario);
                    }
                });
            });
        }

        // 带视图模式的结果显示
        function displayResultsWithView(result, scenario) {
            displayResults(result, scenario);

            // 添加视图说明
            const container = document.getElementById('results');
            let viewDescription = '';

            switch (currentViewMode) {
                case 'current':
                    viewDescription = '📊 当前显示：仅当前时段的耗时数据';
                    break;
                case 'comparison':
                    viewDescription = '📊 当前显示：当前数据 vs 同比数据对比';
                    break;
                case 'difference':
                    viewDescription = '📊 当前显示：相对同比的变化百分比';
                    break;
            }

            // 在结果顶部添加视图说明
            const viewInfo = document.createElement('div');
            viewInfo.style.cssText = 'background: #e3f2fd; padding: 10px; border-radius: 6px; margin-bottom: 15px; font-size: 0.9rem; color: #1565c0;';
            viewInfo.innerHTML = viewDescription;
            container.insertBefore(viewInfo, container.firstChild.nextSibling);
        }

        // 修改运行场景函数
        function runScenario(scenarioName) {
            const scenario = scenarios[scenarioName];
            if (!scenario) return;

            // 显示加载状态
            document.getElementById('loading').style.display = 'block';
            document.getElementById('results').style.display = 'none';

            // 模拟分析过程
            setTimeout(() => {
                try {
                    const result = detectLatencyRiskFromWindows(scenario.current, scenario.yoy, {
                        serviceType: 'MICROSERVICE',
                        enablePatternLearning: true
                    });

                    // 存储当前场景
                    currentScenario = { result, scenario };

                    displayResultsWithView(result, scenario);
                } catch (error) {
                    console.error('分析错误:', error);
                    document.getElementById('loading').innerHTML = `
                        <div style="color: #e74c3c;">
                            <h4>分析出错</h4>
                            <p>${error.message}</p>
                        </div>
                    `;
                }
            }, 500);
        }

        // 从URL参数加载数据
        function loadDataFromParams(paramData) {
            if (paramData.type === 'compressed') {
                // 使用自定义数据创建临时场景
                const customScenario = {
                    name: '自定义数据',
                    description: '从URL加载的自定义数据',
                    current: paramData.currentData,
                    yoy: paramData.yoyData
                };

                // 设置视图模式
                currentViewMode = paramData.viewMode;
                document.querySelectorAll('.view-toggle-btn').forEach(btn => {
                    btn.classList.toggle('active', btn.getAttribute('data-view') === paramData.viewMode);
                });

                // 运行分析
                setTimeout(() => {
                    try {
                        const result = detectLatencyRiskFromWindows(customScenario.current, customScenario.yoy, {
                            serviceType: 'MICROSERVICE',
                            enablePatternLearning: true
                        });

                        currentScenario = { result, scenario: customScenario };
                        displayResultsWithView(result, customScenario);

                        // 添加自定义数据提示
                        const container = document.getElementById('results');
                        const customInfo = document.createElement('div');
                        customInfo.style.cssText = 'background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 6px; margin-bottom: 15px; font-size: 0.9rem; color: #856404;';
                        customInfo.innerHTML = '📊 当前显示：从URL加载的自定义数据';
                        container.insertBefore(customInfo, container.firstChild.nextSibling);

                    } catch (error) {
                        console.error('分析自定义数据失败:', error);
                        document.getElementById('loading').innerHTML = `
                            <div style="color: #e74c3c;">
                                <h4>自定义数据分析失败</h4>
                                <p>${error.message}</p>
                            </div>
                        `;
                    }
                }, 500);

            } else if (paramData.type === 'scenario') {
                // 设置视图模式
                currentViewMode = paramData.viewMode;
                document.querySelectorAll('.view-toggle-btn').forEach(btn => {
                    btn.classList.toggle('active', btn.getAttribute('data-view') === paramData.viewMode);
                });

                // 运行指定场景
                setTimeout(() => runScenario(paramData.scenario), 500);
            }
        }

        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            initViewToggle();

            // 检查URL参数
            const paramData = parseUrlParams();
            if (paramData) {
                loadDataFromParams(paramData);
            } else {
                setTimeout(() => runScenario('normal'), 1000);
            }
        });
    </script>
</body>
</html>
