/**
 * 响应耗时风险检测算法使用示例
 * 
 * 本示例展示如何使用新的耗时风险检测算法，包括：
 * 1. 数据格式和输入要求
 * 2. 配置参数说明
 * 3. 结果解读和告警处理
 * 4. 不同服务类型的最佳实践
 */

const { detectLatencyRiskFromWindows } = require('./latency-risk-detector');

// 示例1: 基础使用 - 微服务API监控
function basicUsageExample() {
    console.log('=== 示例1: 基础使用 - 微服务API监控 ===');
    
    // 当前30分钟的耗时数据（每分钟一个数据点）
    const currentData = [
        // 时间戳: 1757179920 (第1分钟)
        {time: 1757179920, value: "0.025", category: "1"},      // 平均值: 25ms
        {time: 1757179920, value: "0.018", category: "0.5"},   // P50: 18ms
        {time: 1757179920, value: "0.030", category: "0.75"},  // P75: 30ms
        {time: 1757179920, value: "0.055", category: "0.95"},  // P95: 55ms
        {time: 1757179920, value: "0.120", category: "0.99"},  // P99: 120ms
        
        // 时间戳: 1757179980 (第2分钟)
        {time: 1757179980, value: "0.028", category: "1"},
        {time: 1757179980, value: "0.020", category: "0.5"},
        {time: 1757179980, value: "0.032", category: "0.75"},
        {time: 1757179980, value: "0.058", category: "0.95"},
        {time: 1757179980, value: "0.125", category: "0.99"},
        
        // ... 更多数据点
        // 注意：实际使用中需要30个完整的时间点
    ];
    
    // 同比数据（上周同时段）
    const yoyData = [
        {time: 1757179920, value: "0.022", category: "1"},
        {time: 1757179920, value: "0.016", category: "0.5"},
        {time: 1757179920, value: "0.028", category: "0.75"},
        {time: 1757179920, value: "0.050", category: "0.95"},
        {time: 1757179920, value: "0.110", category: "0.99"},
        
        {time: 1757179980, value: "0.024", category: "1"},
        {time: 1757179980, value: "0.018", category: "0.5"},
        {time: 1757179980, value: "0.030", category: "0.75"},
        {time: 1757179980, value: "0.052", category: "0.95"},
        {time: 1757179980, value: "0.115", category: "0.99"},
        
        // ... 更多同比数据
    ];
    
    // 执行风险检测
    const result = detectLatencyRiskFromWindows(currentData, yoyData, {
        serviceType: 'MICROSERVICE',  // 服务类型
        enablePatternLearning: true,  // 启用同比模式学习
        enableAlertSuppression: true  // 启用智能告警抑制
    });
    
    // 结果解读
    console.log('风险检测结果:');
    console.log(`- 综合风险概率: ${(result.risk * 100).toFixed(1)}%`);
    console.log(`- 置信度: ${(result.confidence * 100).toFixed(1)}%`);
    console.log(`- 风险区间: [${(result.riskBand.lower * 100).toFixed(1)}%, ${(result.riskBand.upper * 100).toFixed(1)}%]`);
    
    // 各分位数详情
    console.log('\n各分位数风险详情:');
    Object.entries(result.percentileRisks).forEach(([percentile, risk]) => {
        const name = percentile === '1' ? 'AVG' : `P${(parseFloat(percentile) * 100).toFixed(0)}`;
        console.log(`- ${name}: ${(risk.combinedRisk * 100).toFixed(1)}% (当前: ${(risk.currentValue * 1000).toFixed(1)}ms)`);
    });
    
    // 业务影响评估
    console.log('\n业务影响评估:');
    console.log(`- 用户体验: ${result.businessImpact.userExperience.level}`);
    console.log(`  ${result.businessImpact.userExperience.description}`);
    console.log(`- 系统稳定性: ${result.businessImpact.systemStability.level}`);
    console.log(`  ${result.businessImpact.systemStability.description}`);
    console.log(`- 业务连续性: ${result.businessImpact.businessContinuity.level}`);
    console.log(`  ${result.businessImpact.businessContinuity.description}`);
    
    // 告警处理
    console.log('\n告警信息:');
    if (result.alerts.length === 0) {
        console.log('- 无告警');
    } else {
        result.alerts.forEach((alert, i) => {
            console.log(`- [${alert.level}] ${alert.message}`);
        });
    }
    
    return result;
}

// 示例2: 不同服务类型的配置
function serviceTypeExamples() {
    console.log('\n=== 示例2: 不同服务类型的配置 ===');
    
    // 数据库查询监控
    const databaseConfig = {
        serviceType: 'DATABASE',
        z_th: 1.5,                    // 数据库查询对延迟更敏感
        degradationThreshold: 0.15,   // 15%劣化就需要关注
        absoluteThresholdWeight: 0.5, // 更重视绝对耗时
        relativeThresholdWeight: 0.3,
        patternConsistencyWeight: 0.2
    };
    
    // API网关监控
    const apiGatewayConfig = {
        serviceType: 'API_GATEWAY',
        z_th: 2.0,
        degradationThreshold: 0.2,
        enableAlertSuppression: true,  // 网关流量波动大，启用告警抑制
        peakToleranceMultiplier: 2.0   // 高峰期容忍度更高
    };
    
    // 批处理任务监控
    const batchJobConfig = {
        serviceType: 'BATCH_JOB',
        z_th: 3.0,                    // 批处理任务容忍度更高
        degradationThreshold: 0.5,    // 50%劣化才告警
        absoluteThresholdWeight: 0.2, // 更关注相对变化
        relativeThresholdWeight: 0.6,
        patternConsistencyWeight: 0.2
    };
    
    console.log('不同服务类型的推荐配置:');
    console.log('- 数据库查询: 高敏感度，重视绝对耗时');
    console.log('- API网关: 中等敏感度，启用告警抑制');
    console.log('- 批处理任务: 低敏感度，重视相对变化');
}

// 示例3: 告警集成和处理
function alertIntegrationExample(result) {
    console.log('\n=== 示例3: 告警集成和处理 ===');
    
    // 告警级别映射
    const alertLevelMapping = {
        'CRITICAL': { priority: 1, color: 'red', notify: ['oncall', 'team'] },
        'WARNING': { priority: 2, color: 'orange', notify: ['team'] },
        'INFO': { priority: 3, color: 'blue', notify: ['team'] }
    };
    
    // 处理告警
    result.alerts.forEach(alert => {
        const config = alertLevelMapping[alert.level];
        
        console.log(`处理告警: [${alert.level}] ${alert.message}`);
        console.log(`- 优先级: ${config.priority}`);
        console.log(`- 通知对象: ${config.notify.join(', ')}`);
        
        // 根据告警类型执行不同的处理逻辑
        switch (alert.type) {
            case 'ABSOLUTE_LATENCY_CRITICAL':
                console.log('- 建议: 立即检查系统资源和性能瓶颈');
                break;
            case 'LATENCY_DEGRADATION':
                console.log('- 建议: 对比历史数据，分析性能劣化原因');
                break;
            case 'TAIL_LATENCY_DEGRADATION':
                console.log('- 建议: 检查慢查询和异常请求');
                break;
            case 'PERFORMANCE_IMPROVEMENT':
                console.log('- 建议: 记录性能优化成果，总结经验');
                break;
        }
    });
    
    // 风险等级判断和处理
    const riskLevel = result.risk >= 0.8 ? 'HIGH' :
                     result.risk >= 0.5 ? 'MEDIUM' :
                     result.risk >= 0.2 ? 'LOW' : 'MINIMAL';
    
    console.log(`\n整体风险等级: ${riskLevel}`);
    
    switch (riskLevel) {
        case 'HIGH':
            console.log('- 建议: 立即介入，启动应急响应流程');
            break;
        case 'MEDIUM':
            console.log('- 建议: 密切监控，准备应急预案');
            break;
        case 'LOW':
            console.log('- 建议: 持续观察，记录异常模式');
            break;
        case 'MINIMAL':
            console.log('- 建议: 正常监控即可');
            break;
    }
}

// 示例4: 监控看板集成
function dashboardIntegrationExample(result) {
    console.log('\n=== 示例4: 监控看板集成 ===');
    
    // 生成看板数据
    const dashboardData = {
        // 主要指标
        mainMetrics: {
            riskScore: Math.round(result.risk * 100),
            confidence: Math.round(result.confidence * 100),
            alertCount: result.alerts.length,
            criticalAlertCount: result.alerts.filter(a => a.level === 'CRITICAL').length
        },
        
        // 分位数状态
        percentileStatus: Object.entries(result.percentileRisks).map(([percentile, risk]) => ({
            name: percentile === '1' ? 'AVG' : `P${(parseFloat(percentile) * 100).toFixed(0)}`,
            currentValue: Math.round(risk.currentValue * 1000), // 转换为ms
            riskLevel: risk.combinedRisk >= 0.7 ? 'HIGH' :
                      risk.combinedRisk >= 0.4 ? 'MEDIUM' : 'LOW',
            trend: risk.improvementCount > risk.anomalyCount ? 'IMPROVING' :
                   risk.anomalyCount > 0 ? 'DEGRADING' : 'STABLE'
        })),
        
        // 业务影响摘要
        businessImpact: {
            userExperience: result.businessImpact.userExperience.level,
            systemStability: result.businessImpact.systemStability.level,
            businessContinuity: result.businessImpact.businessContinuity.level
        },
        
        // 时间戳
        timestamp: new Date().toISOString()
    };
    
    console.log('看板数据结构:');
    console.log(JSON.stringify(dashboardData, null, 2));
    
    return dashboardData;
}

// 主函数 - 运行所有示例
function runExamples() {
    // 运行基础示例
    const result = basicUsageExample();
    
    // 运行其他示例
    serviceTypeExamples();
    alertIntegrationExample(result);
    dashboardIntegrationExample(result);
    
    console.log('\n=== 总结 ===');
    console.log('响应耗时风险检测算法的主要特点:');
    console.log('1. 单向异常检测 - 只有耗时增加才是异常');
    console.log('2. 多分位数联合分析 - 全面评估性能状况');
    console.log('3. 同比模式学习 - 自动适应业务高峰特征');
    console.log('4. 智能告警抑制 - 减少误报，提高告警质量');
    console.log('5. 业务影响评估 - 从用户体验角度评估风险');
}

// 如果直接运行此文件，执行示例
if (require.main === module) {
    runExamples();
}

module.exports = {
    basicUsageExample,
    serviceTypeExamples,
    alertIntegrationExample,
    dashboardIntegrationExample,
    runExamples
};
