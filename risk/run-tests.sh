#!/bin/bash

# 流量监控风险检测算法 - 测试运行脚本

echo "🧪 流量监控风险检测算法测试套件"
echo "=================================="
echo

# 检查 Node.js 是否安装
if ! command -v node &> /dev/null; then
    echo "❌ 错误: 未找到 Node.js，请先安装 Node.js"
    exit 1
fi

echo "✅ Node.js 版本: $(node --version)"
echo

# 运行单元测试
echo "🔬 运行单元测试..."
echo "命令: node test-risk-detector.js"
echo "-----------------------------------"
node test-risk-detector.js
test_result=$?

echo
echo "-----------------------------------"

if [ $test_result -eq 0 ]; then
    echo "🎉 单元测试全部通过！"
    echo
    
    # 运行示例演示
    echo "🚀 运行使用示例..."
    echo "命令: node example.js"
    echo "-----------------------------------"
    node example.js
    
    echo
    echo "✅ 测试完成！算法工作正常。"
    echo
    echo "📋 可用命令:"
    echo "  npm test          - 运行单元测试"
    echo "  npm run demo      - 快速演示"
    echo "  node example.js   - 详细示例"
    echo "  node test-risk-detector.js - 直接运行测试"
    
else
    echo "❌ 单元测试失败，请检查算法实现"
    exit 1
fi
