/**
 * 调试特定场景的算法行为
 */

const { detectLatencyRiskFromWindows } = require('./latency-risk-detector');
const { LatencyTestDataGenerator } = require('./latency-risk-detector.test');

function debugSpecificScenarios() {
    const generator = new LatencyTestDataGenerator();
    
    const baseLatencies = {
        '1': 0.02,      // 平均20ms
        '0.5': 0.015,   // P50: 15ms
        '0.75': 0.025,  // P75: 25ms
        '0.95': 0.05,   // P95: 50ms
        '0.99': 0.1     // P99: 100ms
    };

    const times = generator.generateTimePoints(30);

    console.log('=== 调试高峰时段模式场景 ===');
    
    // 高峰时段场景：当前和同比都有高峰特征
    const peakCurrent = generator.generatePeakPattern(times, baseLatencies, 2.0);
    const peakYoy = generator.generatePeakPattern(times, baseLatencies, 1.8);
    
    const peakResult = detectLatencyRiskFromWindows(peakCurrent, peakYoy, {
        serviceType: 'MICROSERVICE'
    });
    
    console.log('\n--- 高峰时段场景分析 ---');
    console.log(`最终风险: ${(peakResult.risk * 100).toFixed(1)}% (期望: 10-40%)`);
    console.log(`置信度: ${(peakResult.confidence * 100).toFixed(1)}%`);
    console.log(`告警数量: ${peakResult.alerts.length} (期望: 0)`);
    
    // 分析同比模式学习效果
    console.log('\n--- 同比模式学习分析 ---');
    Object.entries(peakResult.percentileRisks).forEach(([percentile, risk]) => {
        const pName = percentile === '1' ? 'AVG' : `P${(parseFloat(percentile) * 100).toFixed(0)}`;
        console.log(`${pName}:`);
        console.log(`  同比高峰模式: ${risk.yoyPattern.hasPeakPattern ? '是' : '否'}`);
        console.log(`  高峰强度: ${risk.yoyPattern.peakIntensity.toFixed(2)}`);
        console.log(`  自适应基线调整: ${(risk.adaptiveBaseline.adjustedBaseline / risk.yoyPattern.baseline).toFixed(2)}x`);
        console.log(`  当前值: ${(risk.currentValue * 1000).toFixed(1)}ms`);
        console.log(`  调整后基线: ${(risk.adaptiveBaseline.adjustedBaseline * 1000).toFixed(1)}ms`);
        console.log(`  劣化风险: ${(risk.degradationRisk * 100).toFixed(1)}%`);
        console.log(`  综合风险: ${(risk.combinedRisk * 100).toFixed(1)}%`);
    });
    
    console.log('\n=== 调试性能改善场景 ===');
    
    // 性能改善场景：所有分位数都在下降
    const improvementCurrent = generator.generatePerformanceImprovement(times, baseLatencies, 0.6);
    const improvementYoy = generator.generateNormalLatency(times, baseLatencies, 0.1);
    
    const improvementResult = detectLatencyRiskFromWindows(improvementCurrent, improvementYoy, {
        serviceType: 'MICROSERVICE'
    });
    
    console.log('\n--- 性能改善场景分析 ---');
    console.log(`最终风险: ${(improvementResult.risk * 100).toFixed(1)}% (期望: 0-10%)`);
    console.log(`置信度: ${(improvementResult.confidence * 100).toFixed(1)}%`);
    console.log(`告警数量: ${improvementResult.alerts.length} (期望: 1)`);
    
    // 分析改善检测效果
    console.log('\n--- 性能改善检测分析 ---');
    const totalImprovementCount = Object.values(improvementResult.percentileRisks)
        .reduce((sum, risk) => sum + risk.improvementCount, 0);
    const totalAnomalyCount = Object.values(improvementResult.percentileRisks)
        .reduce((sum, risk) => sum + risk.anomalyCount, 0);
    const totalPointCount = Object.values(improvementResult.percentileRisks)
        .reduce((sum, risk) => sum + risk.pointAnalysis.length, 0);
    
    console.log(`总改善点数: ${totalImprovementCount}`);
    console.log(`总异常点数: ${totalAnomalyCount}`);
    console.log(`总数据点数: ${totalPointCount}`);
    console.log(`改善率: ${(totalImprovementCount / totalPointCount * 100).toFixed(1)}%`);
    console.log(`异常率: ${(totalAnomalyCount / totalPointCount * 100).toFixed(1)}%`);
    console.log(`净改善率: ${((totalImprovementCount - totalAnomalyCount) / totalPointCount * 100).toFixed(1)}%`);
    
    Object.entries(improvementResult.percentileRisks).forEach(([percentile, risk]) => {
        const pName = percentile === '1' ? 'AVG' : `P${(parseFloat(percentile) * 100).toFixed(0)}`;
        console.log(`${pName}:`);
        console.log(`  当前值: ${(risk.currentValue * 1000).toFixed(1)}ms`);
        console.log(`  基线值: ${(risk.baselineValue * 1000).toFixed(1)}ms`);
        console.log(`  改善点数: ${risk.improvementCount}/${risk.pointAnalysis.length}`);
        console.log(`  异常点数: ${risk.anomalyCount}/${risk.pointAnalysis.length}`);
        console.log(`  劣化风险: ${(risk.degradationRisk * 100).toFixed(1)}%`);
        console.log(`  综合风险: ${(risk.combinedRisk * 100).toFixed(1)}%`);
    });
    
    console.log('\n--- 告警详情 ---');
    improvementResult.alerts.forEach((alert, i) => {
        console.log(`${i + 1}. [${alert.level}] ${alert.type}: ${alert.message}`);
    });
    
    return { peakResult, improvementResult };
}

if (require.main === module) {
    debugSpecificScenarios();
}

module.exports = { debugSpecificScenarios };
