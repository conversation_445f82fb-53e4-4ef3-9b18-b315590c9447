<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL传参功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .url-example {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            word-break: break-all;
            border-left: 4px solid #007bff;
        }
        .test-link {
            display: inline-block;
            padding: 8px 16px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin: 5px;
            font-size: 0.9rem;
        }
        .test-link:hover {
            background: #0056b3;
        }
        .compression-info {
            background: #e7f3ff;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #17a2b8;
        }
        .success { color: #28a745; }
        .warning { color: #ffc107; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 URL传参功能测试</h1>
        <p>测试响应耗时风险检测算法的URL传参和数据压缩功能</p>

        <div class="test-section">
            <h3>📊 压缩算法测试</h3>
            <div id="compressionTest">
                <button onclick="testCompression()">运行压缩测试</button>
                <div id="compressionResult"></div>
            </div>
        </div>

        <div class="test-section">
            <h3>🎯 场景参数测试</h3>
            <p>使用预设场景参数生成测试链接：</p>
            
            <div>
                <h4>基础场景链接</h4>
                <a href="latency-demo.html?scenario=normal" class="test-link" target="_blank">正常稳定</a>
                <a href="latency-demo.html?scenario=spike" class="test-link" target="_blank">P99尖刺</a>
                <a href="latency-demo.html?scenario=degradation" class="test-link" target="_blank">系统劣化</a>
                <a href="latency-demo.html?scenario=tail" class="test-link" target="_blank">长尾恶化</a>
                <a href="latency-demo.html?scenario=peak" class="test-link" target="_blank">高峰模式</a>
                <a href="latency-demo.html?scenario=improvement" class="test-link" target="_blank">性能改善</a>
            </div>

            <div>
                <h4>带视图模式的链接</h4>
                <a href="latency-demo.html?scenario=spike&view=comparison" class="test-link" target="_blank">尖刺对比视图</a>
                <a href="latency-demo.html?scenario=degradation&view=difference" class="test-link" target="_blank">劣化差值视图</a>
                <a href="latency-demo.html?scenario=improvement&view=comparison" class="test-link" target="_blank">改善对比视图</a>
            </div>
        </div>

        <div class="test-section">
            <h3>🗜️ 压缩数据传参测试</h3>
            <div>
                <button onclick="generateCompressedUrls()">生成压缩数据链接</button>
                <div id="compressedUrls"></div>
            </div>
        </div>

        <div class="test-section">
            <h3>🔧 完整功能页面测试</h3>
            <p>测试完整功能页面的传参支持：</p>
            
            <div>
                <a href="latency-test.html?scenario=spike&view=comparison&service=DATABASE" class="test-link" target="_blank">数据库尖刺场景</a>
                <a href="latency-test.html?scenario=degradation&view=difference&service=API_GATEWAY" class="test-link" target="_blank">网关劣化场景</a>
            </div>
        </div>

        <div class="test-section">
            <h3>📋 测试说明</h3>
            <div class="compression-info">
                <h4>支持的URL参数：</h4>
                <ul>
                    <li><strong>scenario</strong>: 预设场景 (normal, spike, degradation, tail, peak, improvement)</li>
                    <li><strong>view</strong>: 视图模式 (current, yoy, comparison, difference)</li>
                    <li><strong>service</strong>: 服务类型 (MICROSERVICE, DATABASE, API_GATEWAY, WEB_API, BATCH_JOB)</li>
                    <li><strong>data</strong>: 压缩的当前数据</li>
                    <li><strong>yoy</strong>: 压缩的同比数据 (可选，默认与data相同)</li>
                    <li><strong>z_th</strong>: Z阈值 (默认1.8)</li>
                    <li><strong>deg_th</strong>: 劣化阈值 (默认0.2)</li>
                    <li><strong>pattern</strong>: 模式学习 (true/false，默认true)</li>
                </ul>
            </div>
        </div>
    </div>

    <script src="latency-risk-detector.js"></script>
    <script>
        // 压缩算法测试
        function ultraCompressLatencyData(data) {
            if (!data || data.length === 0) return '';
            
            try {
                const baseTime = data[0].time;
                const interval = 60;
                
                const timeGroups = {};
                data.forEach(item => {
                    const timeIndex = Math.round((item.time - baseTime) / interval);
                    if (!timeGroups[timeIndex]) timeGroups[timeIndex] = {};
                    timeGroups[timeIndex][item.category] = parseFloat(item.value);
                });
                
                let compressed = '';
                compressed += baseTime.toString(36) + '|';
                compressed += interval.toString(36) + '|';
                
                const categories = ['1', '0.5', '0.75', '0.95', '0.99'];
                const timeIndices = Object.keys(timeGroups).map(Number).sort((a, b) => a - b);
                
                const dataGroups = timeIndices.map(timeIndex => {
                    const group = timeGroups[timeIndex];
                    const values = categories.map(cat => {
                        const val = group[cat] || 0;
                        const quantized = Math.min(65535, Math.max(0, Math.round(val * 6553.5)));
                        return quantized;
                    });
                    return values.map(v => v.toString(36)).join('.');
                });
                
                compressed += dataGroups.join(',');
                return compressed;
                
            } catch (error) {
                console.error('压缩失败:', error);
                return '';
            }
        }

        function generateTestData(scenario = 'spike') {
            const baseTime = Math.floor(Date.now() / 1000);
            const baseLatencies = {
                '1': 0.02,      // 平均20ms
                '0.5': 0.015,   // P50: 15ms
                '0.75': 0.025,  // P75: 25ms
                '0.95': 0.05,   // P95: 50ms
                '0.99': 0.1     // P99: 100ms
            };

            const data = [];
            for (let i = 0; i < 20; i++) {
                const time = baseTime + i * 60;
                const isSpike = scenario === 'spike' && i >= 10 && i <= 15;
                
                Object.entries(baseLatencies).forEach(([category, baseValue]) => {
                    let value = baseValue;
                    if (isSpike) {
                        const multiplier = category === '0.99' ? 8 :
                                         category === '0.95' ? 5 :
                                         category === '0.75' ? 2 : 1.2;
                        value = baseValue * multiplier;
                    }
                    
                    data.push({
                        time: time,
                        value: value.toFixed(6),
                        category: category
                    });
                });
            }
            return data;
        }

        function testCompression() {
            const testData = generateTestData('spike');
            const originalJson = JSON.stringify(testData);
            const compressed = ultraCompressLatencyData(testData);
            
            const compressionRatio = ((1 - compressed.length / originalJson.length) * 100).toFixed(1);
            
            document.getElementById('compressionResult').innerHTML = `
                <div class="compression-info">
                    <h4>压缩测试结果：</h4>
                    <p><strong>原始数据：</strong> ${testData.length} 个数据点</p>
                    <p><strong>原始JSON长度：</strong> ${originalJson.length} 字符</p>
                    <p><strong>压缩后长度：</strong> ${compressed.length} 字符</p>
                    <p><strong>压缩率：</strong> <span class="success">${compressionRatio}%</span></p>
                    <p><strong>URL适用性：</strong> ${compressed.length < 2000 ? '<span class="success">✅ 适合URL传参</span>' : '<span class="warning">⚠️ 可能超出URL长度限制</span>'}</p>
                    
                    <h5>压缩数据预览：</h5>
                    <div class="url-example">${compressed.substring(0, 100)}${compressed.length > 100 ? '...' : ''}</div>
                </div>
            `;
        }

        function generateCompressedUrls() {
            const scenarios = ['spike', 'degradation', 'improvement'];
            let html = '<h4>生成的压缩数据链接：</h4>';
            
            scenarios.forEach(scenario => {
                const testData = generateTestData(scenario);
                const compressed = ultraCompressLatencyData(testData);
                
                if (compressed) {
                    const baseUrl = window.location.origin + window.location.pathname.replace('test-url-params.html', '');
                    
                    // 演示页面链接
                    const demoUrl = `${baseUrl}latency-demo.html?data=${encodeURIComponent(compressed)}&view=comparison`;
                    
                    // 完整页面链接
                    const fullUrl = `${baseUrl}latency-test.html?data=${encodeURIComponent(compressed)}&view=comparison&service=MICROSERVICE`;
                    
                    html += `
                        <div style="margin: 15px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                            <h5>${scenario.toUpperCase()} 场景</h5>
                            <p><strong>数据长度：</strong> ${compressed.length} 字符</p>
                            <p><strong>演示页面：</strong></p>
                            <div class="url-example">${demoUrl}</div>
                            <a href="${demoUrl}" class="test-link" target="_blank">打开演示页面</a>
                            
                            <p><strong>完整页面：</strong></p>
                            <div class="url-example">${fullUrl}</div>
                            <a href="${fullUrl}" class="test-link" target="_blank">打开完整页面</a>
                        </div>
                    `;
                }
            });
            
            document.getElementById('compressedUrls').innerHTML = html;
        }

        // 页面加载时运行基础测试
        window.addEventListener('load', () => {
            testCompression();
        });
    </script>
</body>
</html>
