# 外网依赖场景算法优化

## 🎯 **功能概述**

针对业务中常见的外网依赖场景，算法新增了智能识别和处理机制。在高峰期，外网依赖请求增多可能导致P95/P99上升，但这种情况相对安全且可控，不应触发严重告警。

## 🧠 **算法设计思路**

### 外网依赖场景特征
```
典型表现：
- P99/P95: 显著上升 (3-5倍)
- P75: 轻微影响 (1.2倍)
- P50/AVG: 基本稳定
- 同比数据: 可能有类似模式
- 业务影响: 相对可控
```

### 与系统异常的区别
```
系统异常：
- 所有分位数同步上升
- P99极端异常 (>10倍)
- 同比数据通常正常
- 业务影响严重
```

## 🔧 **核心实现**

### 1. 外网依赖模式检测

#### 检测指标
```javascript
const externalDependencyPattern = {
    // 分位数比值特征
    p99_p50_ratio: p99 / p50,           // P99与P50的比值
    p95_p50_ratio: p95 / p50,           // P95与P50的比值
    
    // 稳定性特征
    lowPercentileStable: p50_risk < 0.3 && p75_risk < 0.4,
    highPercentileSpike: p99_p50_ratio > 3 && p95_p50_ratio > 2,
    
    // 同比对比特征
    yoy_similarity: 同比数据相似性分析
};
```

#### 检测逻辑
```javascript
function detectExternalDependencyPattern(percentileRisks, yoyData) {
    let confidence = 0;
    
    // 1. 分位数比值检查 (权重: 0.3-0.4)
    if (p99_p50_ratio > 5 && p95_p50_ratio > 3) confidence += 0.4;
    else if (p99_p50_ratio > 3 && p95_p50_ratio > 2) confidence += 0.3;
    
    // 2. 低分位数稳定性 (权重: 0.25)
    if (p50_risk < 0.3 && p75_risk < 0.4) confidence += 0.25;
    
    // 3. 适度异常检查 (权重: 0.2)
    if (p99_risk > 0.6 && p99_risk < 0.95 && p99_value < 1.0) confidence += 0.2;
    
    // 4. 同比模式相似性 (权重: 0.25)
    if (yoyPattern.hasPeakPattern) confidence += 0.25;
    
    return confidence > 0.5; // 检测阈值
}
```

### 2. 风险评估调整

#### 权重重新分配
```javascript
// 原始权重
const originalWeights = {
    'P99': 0.30, 'P95': 0.25, 'P75': 0.20, 'P50': 0.15, 'AVG': 0.10
};

// 外网依赖场景权重
const externalDependencyWeights = {
    'P99': 0.20, 'P95': 0.20, 'P75': 0.25, 'P50': 0.25, 'AVG': 0.10
};
```

#### 风险折扣机制
```javascript
function calculateExternalDependencyDiscount(pattern, percentileRisks) {
    let discount = 0.8; // 基础折扣
    
    // 根据置信度调整
    discount *= (1 - pattern.confidence * 0.2);
    
    // 根据低分位数稳定性调整
    if (lowPercentileStability > 0.7) discount *= 0.9;
    
    // 根据同比相似性调整
    if (pattern.yoy_similarity > 0.7) discount *= 0.95;
    
    return Math.max(discount, 0.3); // 最低保持30%风险
}
```

### 3. 告警策略优化

#### 告警抑制
```javascript
function shouldSuppressAlert(alert, externalPattern) {
    if (!externalPattern.isExternalDependency) return false;
    
    // 抑制纯粹的高分位数告警
    if (alert.type === 'TAIL_LATENCY_DEGRADATION' && 
        externalPattern.confidence > 0.7) return true;
    
    // 抑制P95/P99劣化告警（如果P50/P75稳定）
    if (alert.type === 'LATENCY_DEGRADATION' && 
        (alert.percentile === 'P95' || alert.percentile === 'P99') &&
        externalPattern.lowPercentileStable) return true;
    
    return false;
}
```

#### 专门告警
```javascript
// 外网依赖专门告警
{
    level: 'INFO',
    type: 'EXTERNAL_DEPENDENCY_IMPACT',
    message: '检测到外网依赖影响，P99/P95上升但低分位数稳定',
    confidence: pattern.confidence,
    evidence: pattern.evidence,
    recommendation: '建议关注外网服务质量，考虑增加超时控制或降级策略'
}
```

### 4. 业务影响重新评估

#### 用户体验评估调整
```javascript
function assessUserExperienceWithExternalDependency(percentileRisks, pattern) {
    if (pattern.isExternalDependency) {
        // 外网依赖场景：主要看P75而不是P99
        const primaryIndicator = p75Value * 0.7 + p50Value * 0.3;
        
        if (primaryIndicator >= 1.0) return 'MODERATE';  // 最高只到MODERATE
        else if (primaryIndicator >= 0.5) return 'MINOR';
        else return 'MINIMAL';
    }
    
    // 正常场景使用原逻辑
    return assessUserExperienceNormal(percentileRisks);
}
```

## 📊 **效果验证**

### 测试场景对比

| 场景 | P99耗时 | P50耗时 | 原始风险 | 调整后风险 | 外网依赖检测 |
|------|---------|---------|----------|------------|--------------|
| **外网依赖** | 500ms | 15ms | 67.5% | 21.1% | ✅ 检测到 (65%) |
| **系统异常** | 300ms | 60ms | 60.3% | 60.3% | ❌ 未检测到 (40%) |

### 关键指标

#### 检测准确性
- **外网依赖识别**: ✅ 正确检测 (置信度65%)
- **系统异常区分**: ✅ 正确区分 (置信度40%，低于50%阈值)
- **风险评分合理性**: ✅ 外网依赖风险(21.1%) < 系统异常(60.3%)

#### 业务友好性
- **误报减少**: 外网依赖场景不再触发CRITICAL告警
- **告警精准**: 提供专门的外网依赖告警和建议
- **影响评估**: 用户体验影响从SIGNIFICANT降为MINOR

## 🎯 **使用场景**

### 适用情况
1. **微服务架构**: 依赖多个外部服务
2. **API网关**: 转发外部API请求
3. **数据同步**: 定期从外部系统拉取数据
4. **第三方集成**: 支付、短信、推送等外部服务

### 典型表现
- 业务高峰期P99/P95上升
- 低分位数(P50/P75)相对稳定
- 同比数据显示类似模式
- 用户体验影响有限

## 🚀 **可视化演示**

### 新增场景
在演示页面中新增"外网依赖"场景按钮，展示：
- P99上升5倍 (100ms → 500ms)
- P95上升3倍 (50ms → 150ms)  
- P75轻微影响 (25ms → 30ms)
- P50基本稳定 (15ms → 15ms)

### 预期结果
- 风险评分: 20-40% (合理范围)
- 告警类型: EXTERNAL_DEPENDENCY_IMPACT (INFO级别)
- 用户体验: MINOR (而非SIGNIFICANT)
- 推荐措施: 关注外网服务质量，考虑超时控制

## 💡 **最佳实践**

### 配置建议
1. **服务类型**: 选择正确的服务类型以获得最佳检测效果
2. **模式学习**: 启用同比模式学习，提高外网依赖检测准确性
3. **阈值调优**: 根据业务特点调整检测敏感度

### 监控策略
1. **分层告警**: 区分外网依赖和系统异常的告警级别
2. **根因分析**: 结合外网依赖检测结果进行问题定位
3. **预防措施**: 基于告警建议优化外网依赖处理策略

## 🔗 **相关文档**

- [算法详细文档](./README.md)
- [可视化指南](./VISUALIZATION_GUIDE.md)
- [调试脚本](./debug-external-dependency.js)
- [测试用例](./latency-risk-detector.test.js)

---

**总结**: 外网依赖场景优化显著提升了算法的业务友好性，减少了误报，同时保持了对真正系统异常的敏感性。通过智能识别、权重调整、风险折扣和专门告警，为用户提供了更精准的耗时异常检测能力。
