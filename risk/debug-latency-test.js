/**
 * 调试和分析耗时风险检测算法的详细行为
 */

const { detectLatencyRiskFromWindows } = require('./latency-risk-detector');
const { LatencyTestDataGenerator } = require('./latency-risk-detector.test');

function debugLatencyDetection() {
    const generator = new LatencyTestDataGenerator();
    
    // 基础耗时配置
    const baseLatencies = {
        '1': 0.02,      // 平均20ms
        '0.5': 0.015,   // P50: 15ms
        '0.75': 0.025,  // P75: 25ms
        '0.95': 0.05,   // P95: 50ms
        '0.99': 0.1     // P99: 100ms
    };

    const times = generator.generateTimePoints(30);

    console.log('=== 调试P99突发尖刺场景 ===');
    
    // 生成突发尖刺数据
    const spikeCurrent = generator.generateSpikeScenario(times, baseLatencies, 20, 25, 8);
    const spikeYoy = generator.generateNormalLatency(times, baseLatencies, 0.1);
    
    const result = detectLatencyRiskFromWindows(spikeCurrent, spikeYoy, {
        serviceType: 'MICROSERVICE'
    });
    
    console.log('\n--- 总体结果 ---');
    console.log(`最终风险: ${(result.risk * 100).toFixed(1)}%`);
    console.log(`置信度: ${(result.confidence * 100).toFixed(1)}%`);
    console.log(`告警数量: ${result.alerts.length}`);
    
    console.log('\n--- 各分位数详细分析 ---');
    Object.entries(result.percentileRisks).forEach(([percentile, risk]) => {
        const pName = percentile === '1' ? 'AVG' : 
                     percentile === '0.99' ? 'P99' : 
                     percentile === '0.95' ? 'P95' : 
                     percentile === '0.75' ? 'P75' : 'P50';
        
        console.log(`\n${pName} (权重: ${(risk.weight * 100).toFixed(0)}%):`);
        console.log(`  当前值: ${(risk.currentValue * 1000).toFixed(1)}ms`);
        console.log(`  基线值: ${(risk.baselineValue * 1000).toFixed(1)}ms`);
        console.log(`  绝对风险: ${(risk.absoluteRisk * 100).toFixed(1)}%`);
        console.log(`  劣化风险: ${(risk.degradationRisk * 100).toFixed(1)}%`);
        console.log(`  一致性风险: ${(risk.consistencyRisk * 100).toFixed(1)}%`);
        console.log(`  综合风险: ${(risk.combinedRisk * 100).toFixed(1)}%`);
        console.log(`  异常点数: ${risk.anomalyCount}/${risk.pointAnalysis.length} (${(risk.anomalyRate * 100).toFixed(1)}%)`);
        console.log(`  改善点数: ${risk.improvementCount}`);
        
        if (risk.yoyPattern) {
            console.log(`  同比基线: ${(risk.yoyPattern.baseline * 1000).toFixed(1)}ms`);
            console.log(`  同比波动性: ${(risk.yoyPattern.volatility * 100).toFixed(1)}%`);
            console.log(`  高峰模式: ${risk.yoyPattern.hasPeakPattern ? '是' : '否'}`);
            console.log(`  尖刺特征: ${risk.yoyPattern.hasSpikiness ? '是' : '否'}`);
        }
        
        if (risk.adaptiveBaseline) {
            console.log(`  自适应基线: ${(risk.adaptiveBaseline.adjustedBaseline * 1000).toFixed(1)}ms`);
            console.log(`  调整因子: ${risk.adaptiveBaseline.adjustmentFactors.length}个`);
        }
    });
    
    console.log('\n--- 告警详情 ---');
    result.alerts.forEach((alert, i) => {
        console.log(`${i + 1}. [${alert.level}] ${alert.type}`);
        console.log(`   ${alert.message}`);
        if (alert.percentile) console.log(`   分位数: ${alert.percentile}`);
        if (alert.currentValue) console.log(`   当前值: ${(alert.currentValue * 1000).toFixed(1)}ms`);
        if (alert.degradationRisk) console.log(`   劣化风险: ${(alert.degradationRisk * 100).toFixed(1)}%`);
    });
    
    console.log('\n--- 业务影响评估 ---');
    console.log(`用户体验: ${result.businessImpact.userExperience.level}`);
    console.log(`  ${result.businessImpact.userExperience.description}`);
    console.log(`系统稳定性: ${result.businessImpact.systemStability.level}`);
    console.log(`  ${result.businessImpact.systemStability.description}`);
    console.log(`业务连续性: ${result.businessImpact.businessContinuity.level}`);
    console.log(`  ${result.businessImpact.businessContinuity.description}`);
    
    console.log('\n--- 聚合信息 ---');
    console.log(`加权平均风险: ${(result.aggregation.weightedAverageRisk * 100).toFixed(1)}%`);
    console.log(`最高分位数风险: ${(result.aggregation.maxPercentileRisk * 100).toFixed(1)}%`);
    console.log(`平均一致性评分: ${(result.aggregation.avgConsistencyScore * 100).toFixed(1)}%`);
    
    // 分析为什么风险评分偏低
    console.log('\n=== 风险评分分析 ===');
    const p99Risk = result.percentileRisks['0.99'];
    if (p99Risk) {
        console.log('P99分析:');
        console.log(`- 当前值: ${(p99Risk.currentValue * 1000).toFixed(1)}ms (基线: ${(p99Risk.yoyPattern.baseline * 1000).toFixed(1)}ms)`);
        console.log(`- 倍数变化: ${(p99Risk.currentValue / p99Risk.yoyPattern.baseline).toFixed(1)}x`);
        console.log(`- 绝对风险评分: ${(p99Risk.absoluteRisk * 100).toFixed(1)}% (阈值: 500ms=${LATENCY_RISK_THRESHOLDS.CRITICAL*1000}ms)`);
        console.log(`- 劣化风险评分: ${(p99Risk.degradationRisk * 100).toFixed(1)}%`);
        console.log(`- 异常点比例: ${(p99Risk.anomalyRate * 100).toFixed(1)}%`);
        
        // 检查最后几个点的情况
        const lastPoints = p99Risk.pointAnalysis.slice(-5);
        console.log('最后5个点的异常情况:');
        lastPoints.forEach((point, i) => {
            console.log(`  ${i+1}: ${(point.current * 1000).toFixed(1)}ms, 异常=${point.isAnomaly}, z=${point.zScore.toFixed(2)}, ratio=${(point.ratio * 100).toFixed(1)}%`);
        });
    }
    
    return result;
}

// 导入阈值常量
const LATENCY_RISK_THRESHOLDS = {
    EXCELLENT: 0.05,
    GOOD: 0.1,
    ACCEPTABLE: 0.2,
    POOR: 0.5,
    CRITICAL: 1.0,
    UNACCEPTABLE: 2.0
};

if (require.main === module) {
    debugLatencyDetection();
}

module.exports = { debugLatencyDetection };
