/**
 * 可视化页面功能测试
 * 验证算法在浏览器环境中的正确性
 */

const { detectLatencyRiskFromWindows } = require('./latency-risk-detector');

// 测试数据生成
function generateTestData() {
    const baseTime = 1757179920;
    const baseLatencies = {
        '1': 0.02,      // 平均20ms
        '0.5': 0.015,   // P50: 15ms
        '0.75': 0.025,  // P75: 25ms
        '0.95': 0.05,   // P95: 50ms
        '0.99': 0.1     // P99: 100ms
    };

    // 生成正常数据
    const normalData = [];
    for (let i = 0; i < 30; i++) {
        const time = baseTime + i * 60;
        Object.entries(baseLatencies).forEach(([category, baseValue]) => {
            const noise = baseValue * (1 + (Math.random() - 0.5) * 0.1);
            normalData.push({
                time: time,
                value: Math.max(0.001, noise).toFixed(6),
                category: category
            });
        });
    }

    // 生成尖刺数据
    const spikeData = [];
    for (let i = 0; i < 30; i++) {
        const time = baseTime + i * 60;
        const isSpike = i >= 20 && i <= 25;
        
        Object.entries(baseLatencies).forEach(([category, baseValue]) => {
            let value = baseValue;
            if (isSpike && category === '0.99') {
                value = baseValue * 8; // P99尖刺
            }
            
            spikeData.push({
                time: time,
                value: Math.max(0.001, value).toFixed(6),
                category: category
            });
        });
    }

    return { normalData, spikeData };
}

// 测试算法功能
function testAlgorithmFunctionality() {
    console.log('🧪 测试算法基础功能...');
    
    const { normalData, spikeData } = generateTestData();
    
    try {
        // 测试正常场景
        const normalResult = detectLatencyRiskFromWindows(normalData, normalData, {
            serviceType: 'MICROSERVICE'
        });
        
        console.log('✅ 正常场景测试通过');
        console.log(`   风险评分: ${(normalResult.risk * 100).toFixed(1)}%`);
        console.log(`   置信度: ${(normalResult.confidence * 100).toFixed(1)}%`);
        console.log(`   告警数量: ${normalResult.alerts.length}`);
        
        // 测试尖刺场景
        const spikeResult = detectLatencyRiskFromWindows(spikeData, normalData, {
            serviceType: 'MICROSERVICE'
        });
        
        console.log('✅ 尖刺场景测试通过');
        console.log(`   风险评分: ${(spikeResult.risk * 100).toFixed(1)}%`);
        console.log(`   置信度: ${(spikeResult.confidence * 100).toFixed(1)}%`);
        console.log(`   告警数量: ${spikeResult.alerts.length}`);
        
        // 验证结果合理性
        if (spikeResult.risk > normalResult.risk) {
            console.log('✅ 风险评分逻辑正确 - 尖刺场景风险更高');
        } else {
            console.log('⚠️  风险评分可能有问题 - 尖刺场景风险应该更高');
        }
        
        return true;
        
    } catch (error) {
        console.error('❌ 算法测试失败:', error.message);
        return false;
    }
}

// 测试数据格式兼容性
function testDataFormatCompatibility() {
    console.log('\n📊 测试数据格式兼容性...');
    
    const testCases = [
        {
            name: '标准格式',
            data: [
                {time: 1757179920, value: "0.025", category: "1"},
                {time: 1757179920, value: "0.055", category: "0.95"},
                {time: 1757179920, value: "0.120", category: "0.99"}
            ]
        },
        {
            name: '数值格式',
            data: [
                {time: 1757179920, value: 0.025, category: "1"},
                {time: 1757179920, value: 0.055, category: "0.95"},
                {time: 1757179920, value: 0.120, category: "0.99"}
            ]
        }
    ];
    
    testCases.forEach(testCase => {
        try {
            // 扩展测试数据
            const extendedData = [];
            for (let i = 0; i < 10; i++) {
                testCase.data.forEach(item => {
                    extendedData.push({
                        ...item,
                        time: item.time + i * 60
                    });
                });
            }
            
            const result = detectLatencyRiskFromWindows(extendedData, extendedData, {
                serviceType: 'MICROSERVICE'
            });
            
            console.log(`✅ ${testCase.name}测试通过`);
            
        } catch (error) {
            console.log(`❌ ${testCase.name}测试失败: ${error.message}`);
        }
    });
}

// 测试配置参数
function testConfigurationOptions() {
    console.log('\n⚙️ 测试配置参数...');
    
    const { normalData } = generateTestData();
    
    const configs = [
        { name: '默认配置', config: {} },
        { name: '高敏感度', config: { z_th: 1.5, degradationThreshold: 0.1 } },
        { name: '低敏感度', config: { z_th: 2.5, degradationThreshold: 0.3 } },
        { name: '数据库服务', config: { serviceType: 'DATABASE' } },
        { name: '禁用模式学习', config: { enablePatternLearning: false } }
    ];
    
    configs.forEach(({ name, config }) => {
        try {
            const result = detectLatencyRiskFromWindows(normalData, normalData, config);
            console.log(`✅ ${name}测试通过 - 风险: ${(result.risk * 100).toFixed(1)}%`);
        } catch (error) {
            console.log(`❌ ${name}测试失败: ${error.message}`);
        }
    });
}

// 性能测试
function testPerformance() {
    console.log('\n⚡ 性能测试...');
    
    const { normalData } = generateTestData();
    
    // 生成大数据集
    const largeData = [];
    for (let i = 0; i < 100; i++) { // 100个时间点
        normalData.slice(0, 5).forEach(item => { // 5个分位数
            largeData.push({
                ...item,
                time: item.time + i * 60
            });
        });
    }
    
    const startTime = Date.now();
    
    try {
        const result = detectLatencyRiskFromWindows(largeData, largeData, {
            serviceType: 'MICROSERVICE'
        });
        
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        console.log(`✅ 性能测试通过`);
        console.log(`   数据点数: ${largeData.length}`);
        console.log(`   处理时间: ${duration}ms`);
        console.log(`   风险评分: ${(result.risk * 100).toFixed(1)}%`);
        
        if (duration < 1000) {
            console.log('✅ 性能表现良好 (< 1秒)');
        } else {
            console.log('⚠️  性能可能需要优化 (> 1秒)');
        }
        
    } catch (error) {
        console.log(`❌ 性能测试失败: ${error.message}`);
    }
}

// 边界条件测试
function testEdgeCases() {
    console.log('\n🔍 边界条件测试...');
    
    const edgeCases = [
        {
            name: '最小数据集',
            current: [
                {time: 1757179920, value: "0.025", category: "1"},
                {time: 1757179920, value: "0.055", category: "0.99"}
            ],
            yoy: [
                {time: 1757179920, value: "0.025", category: "1"},
                {time: 1757179920, value: "0.055", category: "0.99"}
            ]
        },
        {
            name: '极小值',
            current: [
                {time: 1757179920, value: "0.001", category: "1"},
                {time: 1757179920, value: "0.001", category: "0.99"}
            ],
            yoy: [
                {time: 1757179920, value: "0.001", category: "1"},
                {time: 1757179920, value: "0.001", category: "0.99"}
            ]
        },
        {
            name: '极大值',
            current: [
                {time: 1757179920, value: "10.0", category: "1"},
                {time: 1757179920, value: "20.0", category: "0.99"}
            ],
            yoy: [
                {time: 1757179920, value: "0.1", category: "1"},
                {time: 1757179920, value: "0.2", category: "0.99"}
            ]
        }
    ];
    
    edgeCases.forEach(({ name, current, yoy }) => {
        try {
            const result = detectLatencyRiskFromWindows(current, yoy, {
                serviceType: 'MICROSERVICE'
            });
            
            console.log(`✅ ${name}测试通过 - 风险: ${(result.risk * 100).toFixed(1)}%`);
            
        } catch (error) {
            console.log(`❌ ${name}测试失败: ${error.message}`);
        }
    });
}

// 主测试函数
function runVisualizationTests() {
    console.log('🎯 响应耗时风险检测算法可视化测试');
    console.log('==========================================');
    
    const tests = [
        testAlgorithmFunctionality,
        testDataFormatCompatibility,
        testConfigurationOptions,
        testPerformance,
        testEdgeCases
    ];
    
    let passedTests = 0;
    
    tests.forEach(test => {
        try {
            const result = test();
            if (result !== false) {
                passedTests++;
            }
        } catch (error) {
            console.error(`测试执行错误: ${error.message}`);
        }
    });
    
    console.log('\n==========================================');
    console.log(`📊 测试总结: ${passedTests}/${tests.length} 通过`);
    
    if (passedTests === tests.length) {
        console.log('🎉 所有测试通过！可视化页面可以正常使用');
    } else {
        console.log('⚠️  部分测试失败，请检查相关功能');
    }
    
    console.log('\n🚀 启动可视化演示:');
    console.log('   ./start-demo.sh');
    console.log('   或访问: http://localhost:8080/latency-demo.html');
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
    runVisualizationTests();
}

module.exports = {
    runVisualizationTests,
    testAlgorithmFunctionality,
    testDataFormatCompatibility,
    testConfigurationOptions,
    testPerformance,
    testEdgeCases
};
