/**
 * detectLatencyRiskFromWindows - 响应耗时风险检测算法
 * 
 * 专门针对接口响应耗时监控设计，与流量监控的关键差异：
 * 1. 单向异常检测：只有耗时增加才是异常，下降是性能改善
 * 2. 多分位数联合分析：同时考虑平均值、P50、P75、P95、P99
 * 3. 同比模式学习：识别并适应同比数据的固有模式特征
 * 4. 业务影响评估：基于SLA标准评估用户体验影响
 *
 * 输入数据格式:
 * [
 *   {time: 1757179920, value: "0.038671", category: "1"},      // 平均值
 *   {time: 1757179920, value: "0.003268", category: "0.5"},   // P50
 *   {time: 1757179920, value: "0.005103", category: "0.75"},  // P75
 *   {time: 1757179920, value: "0.009691", category: "0.95"},  // P95
 *   {time: 1757179920, value: "0.188735", category: "0.99"},  // P99
 *   ...
 * ]
 *
 * 输出结果: {
 *   risk: float 0..1,                    // 综合风险概率
 *   confidence: float 0..1,              // 置信度
 *   riskBand: {lower, upper},            // 风险置信区间
 *   percentileRisks: {...},              // 各分位数详细风险信息
 *   businessImpact: {...},               // 业务影响评估
 *   yoyPatterns: {...},                  // 同比模式分析
 *   degradationAnalysis: {...},          // 劣化分析
 *   alerts: [...],                       // 智能告警建议
 * }
 */

// 分位数权重配置（基于业界最佳实践）
const PERCENTILE_WEIGHTS = {
    '1': 0.1,      // 平均值 - 基础权重
    '0.5': 0.15,   // P50 - 中等权重  
    '0.75': 0.2,   // P75 - 较高权重
    '0.95': 0.25,  // P95 - 高权重（SLA关键指标）
    '0.99': 0.3    // P99 - 最高权重（用户体验关键）
};

// 基于业界标准的耗时风险阈值（秒）
const LATENCY_RISK_THRESHOLDS = {
    EXCELLENT: 0.05,      // < 50ms
    GOOD: 0.1,            // 50-100ms  
    ACCEPTABLE: 0.2,      // 100-200ms
    POOR: 0.5,            // 200-500ms
    CRITICAL: 1.0,        // 500ms-1s
    UNACCEPTABLE: 2.0     // > 1s
};

// 服务类型基线配置
const SERVICE_TYPE_BASELINES = {
    'DATABASE': { p99: 0.01, p95: 0.005, avg: 0.002 },     // 数据库查询
    'API_GATEWAY': { p99: 0.05, p95: 0.02, avg: 0.01 },   // API网关
    'MICROSERVICE': { p99: 0.1, p95: 0.05, avg: 0.02 },   // 微服务
    'BATCH_JOB': { p99: 5.0, p95: 2.0, avg: 1.0 },        // 批处理任务
    'WEB_API': { p99: 0.2, p95: 0.1, avg: 0.05 },         // Web API
    'DEFAULT': { p99: 0.1, p95: 0.05, avg: 0.02 }         // 默认配置
};

function detectLatencyRiskFromWindows(currentData, yoyData, config = {}) {
    // ---------- 默认配置 ----------
    const defaultConfig = {
        serviceType: config.serviceType ?? 'DEFAULT',
        
        // 基础检测参数
        ewmaSpan: config.ewmaSpan ?? 3,
        madWindow: config.madWindow ?? 7,
        z_th: config.z_th ?? 1.8,           // 提高敏感度
        ratio_th: config.ratio_th ?? 0.12,   // 12%相对变化阈值
        
        // 耗时特化参数
        degradationThreshold: config.degradationThreshold ?? 0.2,  // 20%劣化阈值
        absoluteThresholdWeight: config.absoluteThresholdWeight ?? 0.3,
        relativeThresholdWeight: config.relativeThresholdWeight ?? 0.4,
        patternConsistencyWeight: config.patternConsistencyWeight ?? 0.3,
        
        // 同比模式学习参数
        enablePatternLearning: config.enablePatternLearning ?? true,
        patternAdaptiveStrength: config.patternAdaptiveStrength ?? 0.8,
        peakToleranceMultiplier: config.peakToleranceMultiplier ?? 1.5,
        
        // 告警抑制参数
        enableAlertSuppression: config.enableAlertSuppression ?? true,
        suppressionThreshold: config.suppressionThreshold ?? 0.7,
        
        eps: 1e-9
    };

    // ---------- 工具函数 ----------
    const median = (arr) => {
        if (!arr || arr.length === 0) return 0;
        const a = Array.from(arr).sort((x, y) => x - y);
        const m = Math.floor(a.length / 2);
        return a.length % 2 === 1 ? a[m] : (a[m - 1] + a[m]) / 2;
    };

    const mad = (arr) => {
        if (!arr || arr.length === 0) return 0;
        const med = median(arr);
        const devs = arr.map((x) => Math.abs(x - med));
        return median(devs);
    };

    const mean = (arr) => {
        if (!arr || arr.length === 0) return 0;
        return arr.reduce((sum, x) => sum + x, 0) / arr.length;
    };

    const stddev = (arr) => {
        if (!arr || arr.length <= 1) return 0;
        const m = mean(arr);
        const variance = arr.reduce((sum, x) => sum + (x - m) ** 2, 0) / (arr.length - 1);
        return Math.sqrt(variance);
    };

    const ewma = (vals, span) => {
        if (!vals || vals.length === 0) return [];
        const s = [];
        const alpha = 2 / (Math.max(1, span) + 1);
        let agg = vals[0] ?? 0;
        s[0] = agg;
        for (let i = 1; i < vals.length; i++) {
            agg = alpha * vals[i] + (1 - alpha) * agg;
            s[i] = agg;
        }
        return s;
    };

    // ---------- 数据解析和预处理 ----------
    function parseLatencyData(rawData) {
        const timePoints = {};
        
        // 按时间点分组
        rawData.forEach(item => {
            const time = item.time;
            const category = item.category;
            const value = parseFloat(item.value);
            
            if (!timePoints[time]) {
                timePoints[time] = {};
            }
            timePoints[time][category] = value;
        });
        
        // 提取各分位数时间序列
        const times = Object.keys(timePoints).sort((a, b) => a - b);
        const percentileSeries = {};
        
        Object.keys(PERCENTILE_WEIGHTS).forEach(percentile => {
            percentileSeries[percentile] = {
                times: times,
                values: times.map(time => timePoints[time][percentile] || 0)
            };
        });
        
        return percentileSeries;
    }

    // ---------- 同比模式分析 ----------
    function analyzeYoyLatencyPatterns(yoyPercentileData) {
        const patterns = {};
        
        Object.entries(yoyPercentileData).forEach(([percentile, data]) => {
            const values = data.values;
            const baseline = median(values);
            const volatility = mad(values) / Math.max(baseline, defaultConfig.eps);
            
            // 检测时间模式
            const peaks = findPeaks(values, baseline);
            const valleys = findValleys(values, baseline);
            
            // 计算分位数特征
            const spikiness = calculateSpikiness(values, baseline);
            const gradualTrend = detectGradualTrend(values);
            
            patterns[percentile] = {
                baseline: baseline,
                volatility: volatility,
                
                // 时间模式特征 - 降低高峰模式判断阈值
                hasPeakPattern: peaks.length > values.length * 0.05 || // 5%的点是峰值
                               (peaks.length > 0 && Math.max(...peaks) / baseline > 1.3), // 或有显著峰值
                peakIntensity: peaks.length > 0 ? Math.max(...peaks) / baseline : 1,
                peakFrequency: peaks.length / values.length,
                
                // 分位数特有特征
                hasSpikiness: parseFloat(percentile) >= 0.95 && spikiness > 0.3,
                spikiness: spikiness,
                
                // 趋势特征
                hasGradualIncrease: gradualTrend > 0.1,
                gradualIncreaseRate: gradualTrend,
                
                // 稳定性评估
                stabilityScore: Math.max(0, 1 - volatility),
                
                // 原始数据
                rawValues: values,
                peaks: peaks,
                valleys: valleys
            };
        });
        
        return patterns;
    }

    // ---------- 外网依赖模式检测 ----------
    function detectExternalDependencyPattern(percentileRisks, yoyData) {
        const pattern = {
            isExternalDependency: false,
            confidence: 0,
            evidence: [],
            yoy_similarity: 0,
            lowPercentileStable: false,
            highPercentileSpike: false,
            p99_p50_ratio: 0,
            p95_p50_ratio: 0
        };

        if (!percentileRisks['0.99'] || !percentileRisks['0.5']) {
            return pattern;
        }

        const current = {
            p99: percentileRisks['0.99'].currentValue,
            p95: percentileRisks['0.95']?.currentValue || 0,
            p75: percentileRisks['0.75']?.currentValue || 0,
            p50: percentileRisks['0.5'].currentValue,
            avg: percentileRisks['1']?.currentValue || 0
        };

        // 1. 分位数比值检查 - 外网依赖的典型特征
        const p99_p50_ratio = current.p99 / Math.max(current.p50, 0.001);
        const p95_p50_ratio = current.p95 / Math.max(current.p50, 0.001);

        pattern.p99_p50_ratio = p99_p50_ratio;
        pattern.p95_p50_ratio = p95_p50_ratio;

        // 降低阈值，提高检测敏感度
        if (p99_p50_ratio > 5 && p95_p50_ratio > 3) {
            pattern.evidence.push('高分位数与低分位数差异极显著');
            pattern.confidence += 0.4;
            pattern.highPercentileSpike = true;
        } else if (p99_p50_ratio > 3 && p95_p50_ratio > 2) {
            pattern.evidence.push('高分位数与低分位数差异显著');
            pattern.confidence += 0.3;
            pattern.highPercentileSpike = true;
        }

        // 2. 低分位数稳定性检查
        const p50_risk = percentileRisks['0.5'].degradationRisk;
        const p75_risk = percentileRisks['0.75']?.degradationRisk || 0;

        if (p50_risk < 0.3 && p75_risk < 0.4) {
            pattern.evidence.push('低中分位数保持稳定');
            pattern.confidence += 0.25;
            pattern.lowPercentileStable = true;
        }

        // 3. 高分位数适度异常检查（非极端）
        const p99_risk = percentileRisks['0.99'].degradationRisk;
        const p95_risk = percentileRisks['0.95']?.degradationRisk || 0;

        if (p99_risk > 0.6 && p99_risk < 0.95 && current.p99 < 1.0) { // P99有问题但不是极端异常(<1秒)
            pattern.evidence.push('高分位数适度劣化，非极端异常');
            pattern.confidence += 0.2;
        }

        // 4. 同比模式相似性检查
        if (percentileRisks['0.99'].yoyPattern && percentileRisks['0.95']?.yoyPattern) {
            const p99YoyPattern = percentileRisks['0.99'].yoyPattern;
            const p95YoyPattern = percentileRisks['0.95'].yoyPattern;

            if (p99YoyPattern.hasPeakPattern && p95YoyPattern.hasPeakPattern) {
                pattern.evidence.push('同比数据显示相似的高峰模式');
                pattern.confidence += 0.25;
                pattern.yoy_similarity = 0.8;
            }
        }

        // 降低阈值，提高检测敏感度
        pattern.isExternalDependency = pattern.confidence > 0.5;
        return pattern;
    }

    // 外网依赖场景的权重调整
    function adjustWeightsForExternalDependency(pattern) {
        if (pattern.isExternalDependency) {
            return {
                '0.99': 0.20,  // 降低P99权重
                '0.95': 0.20,  // 降低P95权重
                '0.75': 0.25,  // 提高P75权重
                '0.5': 0.25,   // 提高P50权重
                '1': 0.10      // 保持AVG权重
            };
        }
        return PERCENTILE_WEIGHTS; // 使用原始权重
    }

    // 外网依赖场景的风险折扣
    function calculateExternalDependencyDiscount(pattern, percentileRisks) {
        let discount = 1.0;

        if (pattern.isExternalDependency) {
            // 调整基础折扣，不要过于激进
            discount *= 0.8; // 从0.7调整到0.8

            // 根据置信度调整，减少影响
            discount *= (1 - pattern.confidence * 0.2); // 从0.3调整到0.2

            // 根据低分位数稳定性调整
            const p50_risk = percentileRisks['0.5']?.degradationRisk || 0;
            const p75_risk = percentileRisks['0.75']?.degradationRisk || 0;
            const lowPercentileStability = 1 - (p50_risk + p75_risk) / 2;

            if (lowPercentileStability > 0.7) {
                discount *= 0.9; // 从0.8调整到0.9，减少折扣
            }

            // 根据同比相似性调整
            if (pattern.yoy_similarity > 0.7) {
                discount *= 0.95; // 从0.9调整到0.95，减少折扣
            }
        }

        return Math.max(discount, 0.3); // 最低保持30%风险
    }

    // 辅助函数：寻找峰值 - 改进算法
    function findPeaks(values, baseline) {
        const peaks = [];
        const threshold = baseline * 1.15; // 降低阈值到15%

        // 使用滑动窗口检测峰值
        for (let i = 2; i < values.length - 2; i++) {
            const current = values[i];
            const left = Math.max(values[i-2], values[i-1]);
            const right = Math.max(values[i+1], values[i+2]);

            // 当前值超过阈值且高于左右邻居
            if (current > threshold && current > left && current > right) {
                peaks.push(current);
            }
        }

        // 如果没找到明显峰值，检查是否有持续的高值区间
        if (peaks.length === 0) {
            const highValues = values.filter(v => v > baseline * 1.1);
            if (highValues.length > values.length * 0.2) { // 超过20%的点都偏高
                peaks.push(...highValues.slice(0, 3)); // 取前3个作为代表
            }
        }

        return peaks;
    }

    // 辅助函数：寻找谷值
    function findValleys(values, baseline) {
        const valleys = [];
        const threshold = baseline * 0.8; // 低于基线20%视为谷值
        
        for (let i = 1; i < values.length - 1; i++) {
            if (values[i] < threshold && 
                values[i] < values[i-1] && 
                values[i] < values[i+1]) {
                valleys.push(values[i]);
            }
        }
        return valleys;
    }

    // 辅助函数：计算尖刺性
    function calculateSpikiness(values, baseline) {
        if (values.length === 0) return 0;
        
        const spikes = values.filter(v => v > baseline * 2).length;
        return spikes / values.length;
    }

    // 辅助函数：检测渐进趋势
    function detectGradualTrend(values) {
        if (values.length < 3) return 0;
        
        // 简单线性回归斜率
        const n = values.length;
        const sumX = (n * (n - 1)) / 2;
        const sumY = values.reduce((sum, v) => sum + v, 0);
        const sumXY = values.reduce((sum, v, i) => sum + i * v, 0);
        const sumX2 = (n * (n - 1) * (2 * n - 1)) / 6;
        
        const slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
        const avgY = sumY / n;
        
        return slope / Math.max(avgY, defaultConfig.eps); // 归一化斜率
    }

    // ---------- 数据预处理 ----------
    const currentPercentileData = parseLatencyData(currentData);
    const yoyPercentileData = parseLatencyData(yoyData);
    
    // 验证数据完整性
    const percentiles = Object.keys(PERCENTILE_WEIGHTS);
    for (const percentile of percentiles) {
        if (!currentPercentileData[percentile] || !yoyPercentileData[percentile]) {
            return {
                risk: 0.01,
                confidence: 0,
                error: `Missing data for percentile ${percentile}`,
                reason: 'incomplete_data'
            };
        }
    }

    // 分析同比模式
    const yoyPatterns = analyzeYoyLatencyPatterns(yoyPercentileData);

    // ---------- 自适应基线计算 ----------
    function calculateAdaptiveBaseline(yoyBaseline, yoyPattern, timeContext = {}) {
        let adjustedBaseline = yoyBaseline;
        const adjustmentFactors = [];

        // 1. 时间模式调整 - 增强高峰时段的容忍度
        if (yoyPattern.hasPeakPattern && (timeContext.isInPeakPeriod || true)) {
            // 高峰模式的调整应该更加激进，特别是当高峰强度显著时
            const significantPeak = yoyPattern.peakIntensity > 1.5;
            const adjustmentStrength = significantPeak ? 2.5 : 1.5; // 增加调整强度
            const peakAdjustment = 1 + (yoyPattern.peakIntensity - 1) * adjustmentStrength;
            adjustedBaseline *= peakAdjustment;
            adjustmentFactors.push({
                type: 'peak_pattern',
                factor: peakAdjustment,
                reason: '同比数据显示该时段存在高峰特征'
            });
        }

        // 2. 分位数尖刺特征调整
        if (yoyPattern.hasSpikiness) {
            const spikeToleranceAdjustment = 1 + yoyPattern.spikiness * 0.5;
            adjustedBaseline *= spikeToleranceAdjustment;
            adjustmentFactors.push({
                type: 'spike_tolerance',
                factor: spikeToleranceAdjustment,
                reason: '同比数据显示该分位数存在尖刺特征'
            });
        }

        // 3. 渐进式趋势调整
        if (yoyPattern.hasGradualIncrease) {
            const trendAdjustment = 1 + Math.abs(yoyPattern.gradualIncreaseRate) * 0.3;
            adjustedBaseline *= trendAdjustment;
            adjustmentFactors.push({
                type: 'gradual_trend',
                factor: trendAdjustment,
                reason: '同比数据存在渐进式上升趋势'
            });
        }

        return {
            originalBaseline: yoyBaseline,
            adjustedBaseline: adjustedBaseline,
            adjustmentFactors: adjustmentFactors,
            confidenceLevel: yoyPattern.stabilityScore
        };
    }

    // ---------- 单向异常检测（耗时特化） ----------
    function detectLatencyAnomaly(current, baseline, residual, sigma, config) {
        const z = residual / (sigma + config.eps);
        const ratio = Math.abs(residual) / Math.max(baseline, 1);

        // 关键：只检测正向异常（耗时增加）
        const isPositiveAnomaly = residual > 0 && (
            z >= config.z_th ||
            ratio >= config.ratio_th
        );

        // 耗时下降记录为性能改善 - 降低检测阈值
        const isPerformanceImprovement = residual < 0 && (
            Math.abs(z) >= config.z_th * 0.8 ||  // 降低z阈值
            ratio >= config.ratio_th * 0.6       // 降低比例阈值
        );

        return {
            isAnomaly: isPositiveAnomaly,
            isImprovement: isPerformanceImprovement,
            direction: residual > 0 ? 'DEGRADATION' : 'IMPROVEMENT',
            magnitude: Math.abs(residual),
            zScore: z,
            ratio: ratio,
            residual: residual
        };
    }

    // ---------- 各分位数风险检测 ----------
    const percentileRisks = {};

    for (const percentile of percentiles) {
        const currentSeries = currentPercentileData[percentile];
        const yoySeries = yoyPercentileData[percentile];
        const yoyPattern = yoyPatterns[percentile];

        // 数据平滑
        const currentSmoothed = ewma(currentSeries.values, defaultConfig.ewmaSpan);
        const yoySmoothed = ewma(yoySeries.values, defaultConfig.ewmaSpan);

        // 计算自适应基线
        const adaptiveBaseline = calculateAdaptiveBaseline(
            yoyPattern.baseline,
            yoyPattern
        );

        // 鲁棒回归计算基线和残差
        const N = Math.min(currentSmoothed.length, yoySmoothed.length);
        const ratios = [];
        for (let i = 0; i < N; i++) {
            const denom = (yoySmoothed[i] ?? 0) + defaultConfig.eps;
            ratios.push((currentSmoothed[i] ?? 0) / denom);
        }
        const a = median(ratios);

        const diffs = [];
        for (let i = 0; i < N; i++) {
            diffs.push((currentSmoothed[i] ?? 0) - a * (yoySmoothed[i] ?? 0));
        }
        const b = median(diffs);

        // 计算每个点的异常情况
        const pointAnalysis = [];
        const sigmas = [];

        for (let i = 0; i < N; i++) {
            const baseline = a * (yoySmoothed[i] ?? 0) + b;
            const residual = (currentSmoothed[i] ?? 0) - baseline;

            // 计算动态sigma
            const windowStart = Math.max(0, i - defaultConfig.madWindow + 1);
            const windowEnd = i + 1;
            const residualWindow = [];
            for (let j = windowStart; j < windowEnd; j++) {
                const windowBaseline = a * (yoySmoothed[j] ?? 0) + b;
                const windowResidual = (currentSmoothed[j] ?? 0) - windowBaseline;
                residualWindow.push(windowResidual);
            }

            const madR = mad(residualWindow);
            const sigma = 1.4826 * madR + defaultConfig.eps;
            sigmas.push(sigma);

            // 异常检测
            const anomalyResult = detectLatencyAnomaly(
                currentSmoothed[i],
                baseline,
                residual,
                sigma,
                defaultConfig
            );

            pointAnalysis.push({
                index: i,
                time: currentSeries.times[i],
                current: currentSmoothed[i],
                baseline: baseline,
                adaptiveBaseline: adaptiveBaseline.adjustedBaseline,
                ...anomalyResult
            });
        }

        // 计算该分位数的综合风险
        const anomalyCount = pointAnalysis.filter(p => p.isAnomaly).length;
        const improvementCount = pointAnalysis.filter(p => p.isImprovement).length;
        const lastPoint = pointAnalysis[pointAnalysis.length - 1];

        // 计算历史最大值和尖刺检测
        const maxValue = Math.max(...pointAnalysis.map(p => p.current));
        const maxValueRisk = assessAbsoluteLatencyRisk(maxValue, percentile);
        const spikeIntensity = maxValue / Math.max(yoyPattern.baseline, defaultConfig.eps);

        percentileRisks[percentile] = {
            weight: PERCENTILE_WEIGHTS[percentile],
            yoyPattern: yoyPattern,
            adaptiveBaseline: adaptiveBaseline,
            pointAnalysis: pointAnalysis,
            anomalyCount: anomalyCount,
            improvementCount: improvementCount,
            anomalyRate: anomalyCount / N,
            lastPointRisk: lastPoint ? (lastPoint.isAnomaly ? 1 : 0) : 0,
            currentValue: lastPoint ? lastPoint.current : 0,
            baselineValue: lastPoint ? lastPoint.baseline : 0,
            maxValue: maxValue,
            maxValueRisk: maxValueRisk,
            spikeIntensity: spikeIntensity,
            regressionParams: { a, b },
            medianSigma: median(sigmas)
        };
    }

    // ---------- 绝对耗时风险评估 ----------
    function assessAbsoluteLatencyRisk(currentValue, percentile) {
        const serviceBaseline = SERVICE_TYPE_BASELINES[defaultConfig.serviceType];
        const expectedValue = serviceBaseline[percentile === '1' ? 'avg' :
                                           percentile === '0.95' ? 'p95' :
                                           percentile === '0.99' ? 'p99' : 'avg'];

        // 对于高分位数（P95, P99）使用更严格的阈值
        const isHighPercentile = parseFloat(percentile) >= 0.95;
        const multiplier = isHighPercentile ? 0.5 : 1.0; // 高分位数阈值降低50%

        // 使用更细粒度的风险评分
        if (currentValue >= LATENCY_RISK_THRESHOLDS.UNACCEPTABLE * multiplier) return 1.0;
        if (currentValue >= LATENCY_RISK_THRESHOLDS.CRITICAL * multiplier) return 0.95;
        if (currentValue >= LATENCY_RISK_THRESHOLDS.POOR * multiplier) return 0.8;
        if (currentValue >= LATENCY_RISK_THRESHOLDS.ACCEPTABLE * multiplier) return 0.6;
        if (currentValue >= LATENCY_RISK_THRESHOLDS.GOOD * multiplier) return 0.4;

        // 对于微服务场景，超过100ms就应该有风险评分
        if (isHighPercentile && currentValue >= 0.1) {
            return Math.min(0.3, currentValue / (LATENCY_RISK_THRESHOLDS.GOOD * multiplier) * 0.3);
        }

        return 0.0;
    }

    // ---------- 相对劣化风险评估 ----------
    function assessDegradationRisk(currentValue, baselineValue, adaptiveBaseline, percentileRisk) {
        const relativeChange = (currentValue - baselineValue) / Math.max(baselineValue, defaultConfig.eps);
        const adaptiveChange = (currentValue - adaptiveBaseline.adjustedBaseline) /
                              Math.max(adaptiveBaseline.adjustedBaseline, defaultConfig.eps);

        // 只考虑正向变化（劣化）
        if (adaptiveChange <= 0) return 0.0; // 性能改善或无变化

        // 考虑自适应基线的调整强度 - 如果基线被大幅调整，说明这是预期的模式
        const baselineAdjustmentRatio = adaptiveBaseline.adjustedBaseline / Math.max(baselineValue, defaultConfig.eps);
        const isSignificantAdjustment = baselineAdjustmentRatio > 1.3; // 基线被调整超过30%

        // 如果是显著的基线调整（如高峰模式），大幅降低劣化风险评分
        const adjustmentDiscount = isSignificantAdjustment ? 0.3 : 1.0;

        let riskScore = 0;
        if (adaptiveChange >= 1.0) riskScore = 1.0;      // 100%以上劣化
        else if (adaptiveChange >= 0.5) riskScore = 0.8; // 50%以上劣化
        else if (adaptiveChange >= 0.3) riskScore = 0.5; // 30%以上劣化
        else if (adaptiveChange >= 0.2) riskScore = 0.3; // 20%以上劣化
        else if (adaptiveChange >= 0.1) riskScore = 0.1; // 10%以上劣化

        return riskScore * adjustmentDiscount;
    }

    // ---------- 模式一致性评估 ----------
    function assessPatternConsistency(percentileRisk) {
        const { yoyPattern, anomalyRate, pointAnalysis } = percentileRisk;

        // 计算当前序列与同比模式的一致性
        const currentValues = pointAnalysis.map(p => p.current);
        const currentVolatility = mad(currentValues) / Math.max(median(currentValues), defaultConfig.eps);

        // 波动性一致性
        const volatilityMatch = Math.max(0, 1 - Math.abs(currentVolatility - yoyPattern.volatility) /
                               Math.max(yoyPattern.volatility, 0.1));

        // 异常率一致性（基于同比数据的稳定性）
        const expectedAnomalyRate = Math.max(0.05, 1 - yoyPattern.stabilityScore);
        const anomalyRateMatch = Math.max(0, 1 - Math.abs(anomalyRate - expectedAnomalyRate) /
                                Math.max(expectedAnomalyRate, 0.05));

        // 综合一致性评分
        const overallConsistency = Math.max(0, Math.min(1, volatilityMatch * 0.6 + anomalyRateMatch * 0.4));

        let consistencyLevel;
        if (overallConsistency >= 0.8) consistencyLevel = 'HIGHLY_CONSISTENT';
        else if (overallConsistency >= 0.6) consistencyLevel = 'MODERATELY_CONSISTENT';
        else if (overallConsistency >= 0.4) consistencyLevel = 'PARTIALLY_DEVIATED';
        else if (overallConsistency >= 0.2) consistencyLevel = 'SIGNIFICANTLY_DEVIATED';
        else consistencyLevel = 'COMPLETELY_ANOMALOUS';

        return {
            score: overallConsistency,
            level: consistencyLevel,
            volatilityMatch: volatilityMatch,
            anomalyRateMatch: anomalyRateMatch
        };
    }

    // ---------- 综合风险计算 ----------
    let totalWeightedRisk = 0;
    let totalWeight = 0;
    let maxPercentileRisk = 0;

    // 为每个分位数计算综合风险
    Object.entries(percentileRisks).forEach(([percentile, risk]) => {
        const lastPoint = risk.pointAnalysis[risk.pointAnalysis.length - 1];
        if (!lastPoint) return;

        // 三个维度的风险评估
        const absoluteRisk = assessAbsoluteLatencyRisk(lastPoint.current, percentile);
        const degradationRisk = assessDegradationRisk(
            lastPoint.current,
            lastPoint.baseline,
            risk.adaptiveBaseline,
            risk
        );
        const consistencyAssessment = assessPatternConsistency(risk);
        const consistencyRisk = consistencyAssessment.level === 'COMPLETELY_ANOMALOUS' ? 1.0 :
                               consistencyAssessment.level === 'SIGNIFICANTLY_DEVIATED' ? 0.7 :
                               consistencyAssessment.level === 'PARTIALLY_DEVIATED' ? 0.4 :
                               consistencyAssessment.level === 'MODERATELY_CONSISTENT' ? 0.1 : 0.0;

        // 加权组合三个维度，并考虑异常点密度和尖刺强度
        const anomalyDensityBonus = risk.anomalyRate > 0.3 ? risk.anomalyRate * 0.3 : 0;
        const lastPointBonus = risk.lastPointRisk * 0.2; // 最新点异常加分

        // 尖刺强度加分 - 对于突发高耗时给予更高权重
        // 如果历史最大值风险显著高于当前值，说明有严重尖刺
        const spikeBonus = risk.maxValueRisk > absoluteRisk ?
            (risk.maxValueRisk - absoluteRisk) * 0.6 : 0;

        // 极端尖刺检测 - 如果P99超过500ms或P95超过200ms，直接高风险
        const isExtremeSpike = (percentile === '0.99' && risk.maxValue >= 0.5) ||
                              (percentile === '0.95' && risk.maxValue >= 0.2);
        const extremeSpikeBonus = isExtremeSpike ? 0.4 : 0;

        const combinedRisk = Math.min(1.0, (
            absoluteRisk * defaultConfig.absoluteThresholdWeight +
            degradationRisk * defaultConfig.relativeThresholdWeight +
            consistencyRisk * defaultConfig.patternConsistencyWeight +
            anomalyDensityBonus +
            lastPointBonus +
            spikeBonus +
            extremeSpikeBonus
        ));

        // 更新分位数风险信息
        risk.absoluteRisk = absoluteRisk;
        risk.degradationRisk = degradationRisk;
        risk.consistencyRisk = consistencyRisk;
        risk.consistencyAssessment = consistencyAssessment;
        risk.combinedRisk = combinedRisk;

        // 累计加权风险
        totalWeightedRisk += combinedRisk * risk.weight;
        totalWeight += risk.weight;
        maxPercentileRisk = Math.max(maxPercentileRisk, combinedRisk);
    });

    // 计算最终风险概率
    const weightedAverageRisk = totalWeight > 0 ? totalWeightedRisk / totalWeight : 0;

    // 检测整体性能改善
    const totalImprovementCount = Object.values(percentileRisks)
        .reduce((sum, risk) => sum + risk.improvementCount, 0);
    const totalAnomalyCount = Object.values(percentileRisks)
        .reduce((sum, risk) => sum + risk.anomalyCount, 0);
    const totalPointCount = Object.values(percentileRisks)
        .reduce((sum, risk) => sum + risk.pointAnalysis.length, 0);
    const improvementRate = totalImprovementCount / Math.max(totalPointCount, 1);
    const anomalyRate = totalAnomalyCount / Math.max(totalPointCount, 1);

    // 只有在改善明显超过异常时才应用折扣
    // 如果异常率也很高，说明是混合情况，不应该大幅折扣
    const netImprovementRate = improvementRate - anomalyRate;
    let improvementDiscount = 1.0;

    // 检查是否为真正的性能改善场景
    const hasSevereAnomalies = totalAnomalyCount > totalPointCount * 0.5 || // 异常率超过50%
                              Object.values(percentileRisks).some(risk => risk.maxValue >= 0.5);

    // 检查是否为系统性劣化（所有分位数的劣化风险都很高）
    const highDegradationCount = Object.values(percentileRisks)
        .filter(risk => risk.degradationRisk >= 0.8).length;
    const isSystemicDegradation = highDegradationCount >= 3; // 3个以上分位数严重劣化

    // 只有在没有严重异常且不是系统性劣化时才应用性能改善折扣
    if (!hasSevereAnomalies && !isSystemicDegradation && improvementRate > 0.3) {
        improvementDiscount = 0.1; // 强烈改善，风险极低
    } else if (!hasSevereAnomalies && !isSystemicDegradation && improvementRate > 0.2) {
        improvementDiscount = 0.3; // 明显改善
    } else if (!hasSevereAnomalies && !isSystemicDegradation && netImprovementRate > 0.1) {
        improvementDiscount = 0.6; // 净改善
    }

    // 考虑最高分位数风险的影响（P99异常应该得到更多关注）
    // 增加P99风险的权重，提高对突发尖刺的敏感度
    const p99Risk = percentileRisks['0.99']?.combinedRisk || 0;
    let finalRisk = Math.min(1.0, weightedAverageRisk * 0.6 + maxPercentileRisk * 0.25 + p99Risk * 0.15);

    // 应用性能改善折扣
    finalRisk *= improvementDiscount;

    // ========== 外网依赖模式检测和处理 ==========
    const externalDependencyPattern = detectExternalDependencyPattern(percentileRisks, yoyPercentileData);

    // 如果检测到外网依赖模式，应用相应的风险调整
    if (externalDependencyPattern.isExternalDependency) {
        // 重新计算权重并调整风险
        const adjustedWeights = adjustWeightsForExternalDependency(externalDependencyPattern);

        // 使用调整后的权重重新计算加权风险
        let adjustedWeightedRisk = 0;
        let adjustedTotalWeight = 0;

        Object.entries(percentileRisks).forEach(([percentile, risk]) => {
            const adjustedWeight = adjustedWeights[percentile] || 0;
            adjustedWeightedRisk += risk.combinedRisk * adjustedWeight;
            adjustedTotalWeight += adjustedWeight;
        });

        const reweightedRisk = adjustedTotalWeight > 0 ? adjustedWeightedRisk / adjustedTotalWeight : finalRisk;

        // 应用外网依赖折扣
        const externalDependencyDiscount = calculateExternalDependencyDiscount(externalDependencyPattern, percentileRisks);
        finalRisk = reweightedRisk * externalDependencyDiscount;
    }

    // 检测是否为高峰模式场景，只有在没有严重异常时才应用高峰折扣
    const peakPatternCount = Object.values(percentileRisks)
        .filter(risk => risk.yoyPattern.hasPeakPattern).length;
    const highPercentilePeakCount = ['0.95', '0.99']
        .filter(p => percentileRisks[p]?.yoyPattern.hasPeakPattern).length;

    // 检查是否有严重的绝对耗时问题
    const hasSevereLatencyIssue = Object.values(percentileRisks)
        .some(risk => risk.maxValue >= 0.5 || risk.currentValue >= 0.3); // P99>500ms或当前值>300ms

    // 只有在没有严重耗时问题时才应用高峰模式折扣
    if (!hasSevereLatencyIssue && highPercentilePeakCount >= 2) {
        // P95和P99都有高峰模式，且没有严重耗时问题
        finalRisk *= 0.5; // 进一步降低50%
    } else if (!hasSevereLatencyIssue && peakPatternCount >= 3) {
        // 多个分位数有高峰模式，且没有严重耗时问题
        finalRisk *= 0.7; // 降低30%
    }

    // 计算置信度
    const avgConsistencyScore = Object.values(percentileRisks)
        .reduce((sum, risk) => sum + (risk.consistencyAssessment?.score || 0), 0) / percentiles.length;
    const confidence = Math.max(0.0, Math.min(1.0, avgConsistencyScore * 0.6 + 0.4));

    // ---------- 业务影响评估 ----------
    function assessBusinessImpact(percentileRisks, externalDependencyPattern = null) {
        const p99Risk = percentileRisks['0.99'];
        const p95Risk = percentileRisks['0.95'];
        const p75Risk = percentileRisks['0.75'];
        const p50Risk = percentileRisks['0.5'];
        const avgRisk = percentileRisks['1'];

        const p99Value = p99Risk?.currentValue || 0;
        const p95Value = p95Risk?.currentValue || 0;
        const p75Value = p75Risk?.currentValue || 0;
        const p50Value = p50Risk?.currentValue || 0;
        const avgValue = avgRisk?.currentValue || 0;

        // 用户体验影响评估 - 外网依赖场景下主要看P75而不是P99
        let userExperienceImpact = 'MINIMAL';
        let primaryIndicator = p99Value;

        if (externalDependencyPattern && externalDependencyPattern.isExternalDependency) {
            // 外网依赖场景：用户体验主要看P75，因为P99可能被外网拖累
            primaryIndicator = p75Value * 0.7 + p50Value * 0.3; // 加权评估

            if (primaryIndicator >= 1.0) userExperienceImpact = 'MODERATE';  // 最高只到MODERATE
            else if (primaryIndicator >= 0.5) userExperienceImpact = 'MINOR';
            else if (primaryIndicator >= 0.2) userExperienceImpact = 'MINIMAL';
        } else {
            // 正常场景：主要看P99
            if (p99Value >= 2.0) userExperienceImpact = 'SEVERE';
            else if (p99Value >= 1.0) userExperienceImpact = 'SIGNIFICANT';
            else if (p99Value >= 0.5) userExperienceImpact = 'MODERATE';
            else if (p99Value >= 0.2) userExperienceImpact = 'MINOR';
        }

        // 系统稳定性影响（基于平均值和P95）
        let systemStabilityImpact = 'STABLE';
        if (avgValue >= 1.0 || p95Value >= 1.5) systemStabilityImpact = 'UNSTABLE';
        else if (avgValue >= 0.5 || p95Value >= 1.0) systemStabilityImpact = 'CONCERNING';
        else if (avgValue >= 0.2 || p95Value >= 0.5) systemStabilityImpact = 'WATCHFUL';

        // 业务连续性影响
        let businessContinuityImpact = 'NORMAL';
        const highRiskPercentiles = Object.values(percentileRisks)
            .filter(risk => risk.combinedRisk >= 0.7).length;

        if (highRiskPercentiles >= 3) businessContinuityImpact = 'CRITICAL';
        else if (highRiskPercentiles >= 2) businessContinuityImpact = 'HIGH_RISK';
        else if (highRiskPercentiles >= 1) businessContinuityImpact = 'MODERATE_RISK';

        return {
            userExperience: {
                level: userExperienceImpact,
                p99Latency: p99Value,
                description: generateUXDescription(userExperienceImpact, p99Value)
            },
            systemStability: {
                level: systemStabilityImpact,
                avgLatency: avgValue,
                p95Latency: p95Value,
                description: generateStabilityDescription(systemStabilityImpact)
            },
            businessContinuity: {
                level: businessContinuityImpact,
                affectedPercentiles: highRiskPercentiles,
                description: generateBusinessDescription(businessContinuityImpact)
            }
        };
    }

    function generateUXDescription(level, p99Value) {
        switch (level) {
            case 'SEVERE': return `P99耗时${(p99Value*1000).toFixed(0)}ms，用户体验严重受损`;
            case 'SIGNIFICANT': return `P99耗时${(p99Value*1000).toFixed(0)}ms，用户体验显著下降`;
            case 'MODERATE': return `P99耗时${(p99Value*1000).toFixed(0)}ms，部分用户可能感知到延迟`;
            case 'MINOR': return `P99耗时${(p99Value*1000).toFixed(0)}ms，轻微影响用户体验`;
            default: return '用户体验影响最小';
        }
    }

    function generateStabilityDescription(level) {
        switch (level) {
            case 'UNSTABLE': return '系统响应不稳定，可能存在性能瓶颈';
            case 'CONCERNING': return '系统性能下降，需要关注';
            case 'WATCHFUL': return '系统性能轻微波动，建议监控';
            default: return '系统运行稳定';
        }
    }

    function generateBusinessDescription(level) {
        switch (level) {
            case 'CRITICAL': return '多个关键指标异常，业务连续性面临风险';
            case 'HIGH_RISK': return '关键性能指标异常，可能影响业务';
            case 'MODERATE_RISK': return '部分性能指标异常，建议关注';
            default: return '业务运行正常';
        }
    }

    // ---------- 智能告警生成 ----------
    function generateLatencyAlerts(percentileRisks, businessImpact, finalRisk, confidence) {
        const alerts = [];

        // 绝对耗时告警 - 考虑历史最大值
        Object.entries(percentileRisks).forEach(([percentile, risk]) => {
            const currentValue = risk.currentValue;
            const maxValue = risk.maxValue; // 历史最大值
            const percentileName = percentile === '1' ? 'AVG' :
                                  percentile === '0.99' ? 'P99' :
                                  percentile === '0.95' ? 'P95' :
                                  percentile === '0.75' ? 'P75' : 'P50';

            // 对于高分位数使用更严格的阈值
            const isHighPercentile = parseFloat(percentile) >= 0.95;
            const criticalThreshold = isHighPercentile ?
                LATENCY_RISK_THRESHOLDS.CRITICAL * 0.5 : LATENCY_RISK_THRESHOLDS.CRITICAL;
            const poorThreshold = isHighPercentile ?
                LATENCY_RISK_THRESHOLDS.POOR * 0.5 : LATENCY_RISK_THRESHOLDS.POOR;

            // 检查当前值或历史最大值，但要考虑高峰模式
            const checkValue = Math.max(currentValue, maxValue);
            const hasHighPeakPattern = risk.yoyPattern.hasPeakPattern && risk.yoyPattern.peakIntensity > 1.5;

            // 如果有显著高峰模式，提高告警阈值
            const adjustedCriticalThreshold = hasHighPeakPattern ? criticalThreshold * 1.5 : criticalThreshold;
            const adjustedPoorThreshold = hasHighPeakPattern ? poorThreshold * 1.5 : poorThreshold;

            if (checkValue >= adjustedCriticalThreshold) {
                alerts.push({
                    level: 'CRITICAL',
                    type: 'ABSOLUTE_LATENCY_CRITICAL',
                    percentile: percentileName,
                    message: `${percentileName}耗时${(checkValue*1000).toFixed(0)}ms，超过业界可接受阈值`,
                    currentValue: currentValue,
                    maxValue: maxValue,
                    threshold: adjustedCriticalThreshold,
                    priority: 1,
                    ignoreYoyPattern: true
                });
            } else if (checkValue >= adjustedPoorThreshold) {
                alerts.push({
                    level: 'WARNING',
                    type: 'ABSOLUTE_LATENCY_WARNING',
                    percentile: percentileName,
                    message: `${percentileName}耗时${(checkValue*1000).toFixed(0)}ms，接近性能阈值`,
                    currentValue: currentValue,
                    maxValue: maxValue,
                    threshold: adjustedPoorThreshold,
                    priority: 2
                });
            }
        });

        // 劣化告警 - 只对关键分位数告警，避免告警泛滥
        const degradedPercentiles = Object.entries(percentileRisks)
            .filter(([percentile, risk]) => risk.degradationRisk >= 0.5)
            .sort(([,a], [,b]) => b.degradationRisk - a.degradationRisk); // 按劣化程度排序

        // 只对最严重的2个分位数告警
        degradedPercentiles.slice(0, 2).forEach(([percentile, risk]) => {
            const percentileName = percentile === '1' ? 'AVG' :
                                  percentile === '0.99' ? 'P99' :
                                  percentile === '0.95' ? 'P95' :
                                  percentile === '0.75' ? 'P75' : 'P50';

            alerts.push({
                level: 'WARNING',
                type: 'LATENCY_DEGRADATION',
                percentile: percentileName,
                message: `${percentileName}耗时相对同比显著劣化`,
                degradationRisk: risk.degradationRisk,
                consistencyLevel: risk.consistencyAssessment?.level || 'UNKNOWN',
                priority: 2
            });
        });

        // 长尾恶化告警
        const p99Risk = percentileRisks['0.99'];
        const avgRisk = percentileRisks['1'];
        if (p99Risk && avgRisk &&
            p99Risk.degradationRisk >= 0.5 && avgRisk.degradationRisk <= 0.2) {
            alerts.push({
                level: 'WARNING',
                type: 'TAIL_LATENCY_DEGRADATION',
                message: '长尾耗时恶化，P99显著上升但平均值稳定，用户体验可能受影响',
                p99Degradation: p99Risk.degradationRisk,
                avgDegradation: avgRisk.degradationRisk,
                priority: 2
            });
        }

        // 性能改善通知 - 只有在非系统性劣化时才通知
        const improvementCount = Object.values(percentileRisks)
            .reduce((count, risk) => count + risk.improvementCount, 0);
        const totalPoints = Object.values(percentileRisks)
            .reduce((count, risk) => count + risk.pointAnalysis.length, 0);

        // 检查是否为系统性劣化
        const highDegradationCount = Object.values(percentileRisks)
            .filter(risk => risk.degradationRisk >= 0.8).length;
        const isSystemicDegradation = highDegradationCount >= 3;

        if (!isSystemicDegradation && improvementCount / totalPoints >= 0.3) {
            alerts.push({
                level: 'INFO',
                type: 'PERFORMANCE_IMPROVEMENT',
                message: '检测到性能改善，多个分位数耗时显著下降',
                improvementRate: improvementCount / totalPoints,
                priority: 4
            });
        }

        // ========== 外网依赖相关告警处理 ==========

        // 外网依赖模式告警抑制
        function shouldSuppressAlert(alert, externalPattern) {
            if (!externalPattern.isExternalDependency) return false;

            // 抑制纯粹的高分位数告警
            if (alert.type === 'TAIL_LATENCY_DEGRADATION' &&
                externalPattern.confidence > 0.7) {
                return true;
            }

            // 抑制P95/P99的劣化告警（如果P50/P75稳定）
            if (alert.type === 'LATENCY_DEGRADATION' &&
                (alert.percentile === 'P95' || alert.percentile === 'P99') &&
                externalPattern.lowPercentileStable) {
                return true;
            }

            return false;
        }

        // 应用告警抑制
        const filteredAlerts = alerts.filter(alert => !shouldSuppressAlert(alert, externalDependencyPattern));
        alerts.length = 0;
        alerts.push(...filteredAlerts);

        // 生成外网依赖专门告警
        if (externalDependencyPattern.isExternalDependency && externalDependencyPattern.confidence > 0.7) {
            alerts.push({
                level: 'INFO',
                type: 'EXTERNAL_DEPENDENCY_IMPACT',
                message: `检测到外网依赖影响，P99/P95上升但低分位数稳定`,
                confidence: externalDependencyPattern.confidence,
                evidence: externalDependencyPattern.evidence,
                p99_p50_ratio: externalDependencyPattern.p99_p50_ratio.toFixed(1),
                p95_p50_ratio: externalDependencyPattern.p95_p50_ratio.toFixed(1),
                recommendation: '建议关注外网服务质量，考虑增加超时控制或降级策略',
                priority: 3
            });
        }

        // 综合风险告警 - 降低置信度要求
        if (finalRisk >= 0.8 && confidence >= 0.3) {
            alerts.push({
                level: 'CRITICAL',
                type: 'HIGH_OVERALL_RISK',
                message: `综合耗时风险${(finalRisk*100).toFixed(1)}%，置信度${(confidence*100).toFixed(0)}%`,
                risk: finalRisk,
                confidence: confidence,
                priority: 1
            });
        }

        return alerts.sort((a, b) => a.priority - b.priority);
    }

    // 执行业务影响评估和告警生成
    const businessImpact = assessBusinessImpact(percentileRisks, externalDependencyPattern);
    const alerts = generateLatencyAlerts(percentileRisks, businessImpact, finalRisk, confidence);

    return {
        risk: Math.round(finalRisk * 1000) / 1000,
        confidence: Math.round(confidence * 1000) / 1000,
        riskBand: {
            lower: Math.max(0, finalRisk - (1 - confidence) * 0.2),
            upper: Math.min(1, finalRisk + (1 - confidence) * 0.2)
        },
        percentileRisks: percentileRisks,
        businessImpact: businessImpact,
        alerts: alerts,
        yoyPatterns: yoyPatterns,
        externalDependencyPattern: externalDependencyPattern,
        aggregation: {
            weightedAverageRisk: weightedAverageRisk,
            maxPercentileRisk: maxPercentileRisk,
            avgConsistencyScore: avgConsistencyScore
        },
        config: defaultConfig
    };
}

// 导出函数 - 支持浏览器和Node.js环境
if (typeof module !== 'undefined' && module.exports) {
    // Node.js环境
    module.exports = { detectLatencyRiskFromWindows, PERCENTILE_WEIGHTS, LATENCY_RISK_THRESHOLDS };
} else if (typeof window !== 'undefined') {
    // 浏览器环境
    window.detectLatencyRiskFromWindows = detectLatencyRiskFromWindows;
    window.PERCENTILE_WEIGHTS = PERCENTILE_WEIGHTS;
    window.LATENCY_RISK_THRESHOLDS = LATENCY_RISK_THRESHOLDS;
}
