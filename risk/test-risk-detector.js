/**
 * 流量监控风险检测算法单元测试
 * 基于业界最佳实践的测试用例
 */

const fs = require('fs');
const path = require('path');

// 加载算法
const algorithmCode = fs.readFileSync(path.join(__dirname, 'risk-detector.js'), 'utf8');
eval(algorithmCode);

// 测试框架
class TestFramework {
    constructor() {
        this.tests = [];
        this.passed = 0;
        this.failed = 0;
    }

    test(name, testFn) {
        this.tests.push({ name, testFn });
    }

    async run() {
        console.log('🧪 流量监控风险检测算法 - 单元测试');
        console.log('=' .repeat(60));
        console.log();

        for (const { name, testFn } of this.tests) {
            try {
                await testFn();
                console.log(`✅ ${name}`);
                this.passed++;
            } catch (error) {
                console.log(`❌ ${name}`);
                console.log(`   错误: ${error.message}`);
                this.failed++;
            }
        }

        console.log();
        console.log('=' .repeat(60));
        console.log(`📊 测试结果: ${this.passed} 通过, ${this.failed} 失败`);
        console.log(`🎯 通过率: ${((this.passed / (this.passed + this.failed)) * 100).toFixed(1)}%`);
        
        if (this.failed === 0) {
            console.log('🎉 所有测试通过！算法符合业界最佳实践！');
        } else {
            console.log('⚠️  部分测试失败，需要进一步优化');
        }

        return this.failed === 0;
    }

    assert(condition, message) {
        if (!condition) {
            throw new Error(message);
        }
    }

    assertRiskInRange(actual, min, max, scenario) {
        const risk = actual * 100;
        if (risk < min || risk > max) {
            throw new Error(`${scenario}: 期望风险 ${min}-${max}%, 实际 ${risk.toFixed(1)}%`);
        }
    }

    assertRiskAbove(actual, threshold, scenario) {
        const risk = actual * 100;
        if (risk < threshold) {
            throw new Error(`${scenario}: 期望风险 ≥${threshold}%, 实际 ${risk.toFixed(1)}%`);
        }
    }

    assertRiskBelow(actual, threshold, scenario) {
        const risk = actual * 100;
        if (risk > threshold) {
            throw new Error(`${scenario}: 期望风险 ≤${threshold}%, 实际 ${risk.toFixed(1)}%`);
        }
    }
}

// 创建测试实例
const test = new TestFramework();

// 测试用例 1: 正常流量波动
test.test('正常流量波动 (±5%)', () => {
    const current = [1050, 1020, 980, 1030, 990, 1060, 1010, 970, 1040, 1000];
    const yoy = [1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000];
    
    const result = detectRiskFromWindows(current, yoy);
    
    test.assertRiskBelow(result.risk, 5, '正常波动应该是低风险');
    test.assert(result.dataRelation.relationType === 'TRAFFIC_MONITORING', '应该识别为流量监控类型');
    test.assert(Math.abs(result.dataRelation.flowChangePercent) < 10, '流量变化应该小于10%');
});

// 测试用例 2: 轻微流量增长
test.test('轻微流量增长 (+15%)', () => {
    const current = [1150, 1120, 1180, 1130, 1190, 1160, 1110, 1170, 1140, 1100];
    const yoy = [1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000];
    
    const result = detectRiskFromWindows(current, yoy);
    
    test.assertRiskInRange(result.risk, 2, 10, '15%增长应该是轻微关注');
    test.assert(result.dataRelation.flowChangePercent > 10, '应该检测到15%左右的增长');
});

// 测试用例 3: 需要关注的流量增长
test.test('需要关注的流量增长 (+25%)', () => {
    const current = [1250, 1220, 1280, 1230, 1290, 1260, 1210, 1270, 1240, 1200];
    const yoy = [1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000];
    
    const result = detectRiskFromWindows(current, yoy);
    
    test.assertRiskInRange(result.risk, 5, 20, '25%增长应该需要关注');
    test.assert(result.dataRelation.flowChangePercent > 20, '应该检测到25%左右的增长');
});

// 测试用例 4: 中等风险流量增长
test.test('中等风险流量增长 (+50%)', () => {
    const current = [1500, 1520, 1480, 1530, 1490, 1560, 1510, 1470, 1540, 1500];
    const yoy = [1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000];
    
    const result = detectRiskFromWindows(current, yoy);
    
    test.assertRiskInRange(result.risk, 20, 40, '50%增长应该是中等风险');
    test.assert(result.dataRelation.flowChangePercent > 45, '应该检测到50%左右的增长');
});

// 测试用例 5: 高风险流量增长
test.test('高风险流量增长 (+80%)', () => {
    const current = [1800, 1820, 1780, 1830, 1790, 1860, 1810, 1770, 1840, 1800];
    const yoy = [1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000];
    
    const result = detectRiskFromWindows(current, yoy);
    
    test.assertRiskInRange(result.risk, 30, 60, '80%增长应该是高风险');
    test.assert(result.dataRelation.flowChangePercent > 75, '应该检测到80%左右的增长');
});

// 测试用例 6: 极高风险 - 流量翻倍
test.test('极高风险 - 流量翻倍 (+100%)', () => {
    const current = [2000, 2020, 1980, 2030, 1990, 2060, 2010, 1970, 2040, 2000];
    const yoy = [1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000];
    
    const result = detectRiskFromWindows(current, yoy);
    
    test.assertRiskAbove(result.risk, 80, '流量翻倍应该是极高风险');
    test.assert(result.dataRelation.flowChangePercent > 95, '应该检测到100%左右的增长');
});

// 测试用例 7: 服务异常 - 流量大幅下降
test.test('服务异常 - 流量大幅下降 (-70%)', () => {
    const current = [300, 320, 280, 330, 290, 360, 310, 270, 340, 300];
    const yoy = [1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000];
    
    const result = detectRiskFromWindows(current, yoy);
    
    test.assertRiskAbove(result.risk, 25, '流量大幅下降应该是高风险');
    test.assert(result.dataRelation.flowChangePercent < -60, '应该检测到70%左右的下降');
});

// 测试用例 8: 小流量服务正常波动
test.test('小流量服务正常波动', () => {
    const current = [8, 12, 6, 15, 9, 18, 10, 5, 14, 8];
    const yoy = [10, 10, 10, 10, 10, 10, 10, 10, 10, 10];
    
    const result = detectRiskFromWindows(current, yoy);
    
    test.assertRiskBelow(result.risk, 10, '小流量服务的正常波动应该是低风险');
    test.assert(result.dataRelation.isLowTrafficService, '应该识别为小流量服务');
});

// 测试用例 9: 小流量服务异常增长
test.test('小流量服务异常增长', () => {
    const current = [35, 40, 32, 45, 38, 50, 36, 30, 42, 35];
    const yoy = [10, 10, 10, 10, 10, 10, 10, 10, 10, 10];

    const result = detectRiskFromWindows(current, yoy);

    test.assertRiskInRange(result.risk, 40, 70, '小流量服务异常增长应该是中高风险');
    test.assert(result.dataRelation.isLowTrafficService, '应该识别为小流量服务');
});

// 测试用例 10: 正常倍数格式YoY
test.test('正常倍数格式YoY数据', () => {
    const current = [1000, 1050, 980, 1120, 1200, 1350, 1280, 1150, 1080, 950];
    const yoy = [0.95, 1.0, 1.02, 0.98, 1.05, 1.1, 1.08, 1.03, 0.99, 0.96];

    const result = detectRiskFromWindows(current, yoy);

    test.assertRiskBelow(result.risk, 5, '正常倍数格式应该是低风险');
    test.assert(result.dataRelation.relationType === 'RATIO_MULTIPLIER', '应该识别为倍数格式');
});

// 测试用例 11: 正常百分比格式YoY
test.test('正常百分比格式YoY数据', () => {
    const current = [1000, 1050, 980, 1120, 1200, 1350, 1280, 1150, 1080, 950];
    const yoy = [95, 100, 102, 98, 105, 110, 108, 103, 99, 96];

    const result = detectRiskFromWindows(current, yoy);

    test.assertRiskBelow(result.risk, 5, '正常百分比格式应该是低风险');
    test.assert(result.dataRelation.relationType === 'PERCENTAGE', '应该识别为百分比格式');
});

// 测试用例 12: 极端异常数据
test.test('极端异常数据 - 万倍暴增', () => {
    const current = [1003333, 10522222, 983333, 112222, 320333];
    const yoy = [95, 100, 102, 98, 105];

    const result = detectRiskFromWindows(current, yoy);

    test.assertRiskAbove(result.risk, 90, '极端异常应该是最高风险');
    test.assert(result.dataRelation.relationType === 'EXTREME_SURGE', '应该识别为极端暴增');
    test.assert(result.confidence > 0.9, '极端情况应该有高置信度');
});

// 测试用例 13: DDoS攻击模式
test.test('DDoS攻击模式 - 百倍暴增', () => {
    const current = [150000, 180000, 220000, 350000, 500000];
    const yoy = [1000, 1050, 980, 1120, 1200];

    const result = detectRiskFromWindows(current, yoy);

    test.assertRiskAbove(result.risk, 80, 'DDoS攻击应该是极高风险');
    test.assert(result.dataRelation.relationType === 'MAJOR_SURGE', '应该识别为重大暴增');
});

// 测试用例 14: 流量波动性检测
test.test('流量波动性检测', () => {
    const current = [1000, 1500, 800, 1800, 600, 2000, 500, 1200, 900, 1100];
    const yoy = [1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000];

    const result = detectRiskFromWindows(current, yoy);

    test.assertRiskAbove(result.risk, 1, '波动性应该产生一定风险');
    test.assert(result.risk > 0, '应该检测到风险');
});

// 测试用例 15: 边界条件 - 零流量
test.test('边界条件 - 零流量', () => {
    const current = [0, 0, 0, 0, 0];
    const yoy = [1000, 1000, 1000, 1000, 1000];

    const result = detectRiskFromWindows(current, yoy);

    test.assertRiskAbove(result.risk, 50, '零流量应该是高风险');
    test.assert(result.dataRelation.relationType === 'MAJOR_DROP', '应该识别为重大下降');
});

// 测试用例 16: 数据质量检测
test.test('数据质量检测 - 缺失值处理', () => {
    const current = [1000, null, 1050, undefined, 980];
    const yoy = [1000, 1000, null, 1000, undefined];

    const result = detectRiskFromWindows(current, yoy);

    test.assert(result.risk >= 0, '应该能处理缺失值并返回有效结果');
    test.assert(result.confidence >= 0, '置信度应该是有效值');
});

// 测试用例 17: 持续异常检测
test.test('持续异常检测', () => {
    const current = [1500, 1520, 1480, 1530, 1490, 1560, 1510, 1470, 1540, 1500];
    const yoy = [1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000];

    const result = detectRiskFromWindows(current, yoy);

    test.assert(result.risk > 0.2, '持续50%增长应该产生中等风险');
    test.assert(result.dataRelation.flowChangePercent > 40, '应该检测到50%左右的增长');
});

// 测试用例 18: 置信度评估
test.test('置信度评估', () => {
    const current = [1200, 1220, 1180, 1230, 1190];
    const yoy = [1000, 1000, 1000, 1000, 1000];

    const result = detectRiskFromWindows(current, yoy);

    test.assert(result.confidence >= 0.3 && result.confidence <= 1.0, '置信度应该在合理范围内');
    test.assert(result.riskBand && result.riskBand.lower && result.riskBand.upper, '应该提供风险区间');
});

// 测试用例 19: 瞬时流量尖峰
test.test('瞬时流量尖峰 - 单点异常', () => {
    const current = [1000, 1020, 15000, 1030, 990, 1050, 1010, 980, 1040, 1000];
    const yoy = [1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000];

    const result = detectRiskFromWindows(current, yoy);

    test.assertRiskAbove(result.risk, 1, '瞬时尖峰应该产生一定风险');
    test.assert(result.perPoint.some(p => p.isAnomaly), '应该检测到异常点');
});

// 测试用例 20: 高流量服务正常波动 (百万级)
test.test('高流量服务正常波动 (百万级)', () => {
    const current = [1050000, 1020000, 980000, 1030000, 990000, 1060000, 1010000, 970000, 1040000, 1000000];
    const yoy = [1000000, 1000000, 1000000, 1000000, 1000000, 1000000, 1000000, 1000000, 1000000, 1000000];

    const result = detectRiskFromWindows(current, yoy);

    test.assertRiskBelow(result.risk, 5, '高流量服务正常波动应该是低风险');
    test.assert(!result.dataRelation.isLowTrafficService, '应该识别为高流量服务');
});

// 测试用例 21: 高流量服务异常暴增 (百万级)
test.test('高流量服务异常暴增 (百万级)', () => {
    const current = [1500000, 1520000, 1480000, 1530000, 1490000, 1560000, 1510000, 1470000, 1540000, 1500000];
    const yoy = [1000000, 1000000, 1000000, 1000000, 1000000, 1000000, 1000000, 1000000, 1000000, 1000000];

    const result = detectRiskFromWindows(current, yoy);

    test.assertRiskAbove(result.risk, 20, '高流量服务50%增长应该是中等风险');
    test.assert(result.dataRelation.flowChangePercent > 45, '应该检测到50%左右的增长');
});

// 测试用例 22: 极低流量服务 (个位数)
test.test('极低流量服务 (个位数)', () => {
    const current = [3, 5, 2, 7, 4, 8, 3, 1, 6, 3];
    const yoy = [4, 4, 4, 4, 4, 4, 4, 4, 4, 4];

    const result = detectRiskFromWindows(current, yoy);

    test.assertRiskBelow(result.risk, 15, '极低流量服务正常波动应该容忍');
    test.assert(result.dataRelation.isLowTrafficService, '应该识别为小流量服务');
});

// 测试用例 23: 新服务 - 无历史数据
test.test('新服务 - 无历史数据', () => {
    const current = [100, 120, 80, 150, 90, 180, 110, 70, 140, 100];
    const yoy = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0];

    const result = detectRiskFromWindows(current, yoy);

    test.assertRiskAbove(result.risk, 50, '新服务无历史数据应该是高风险');
    test.assert(result.dataRelation.relationType === 'MAJOR_SURGE' || result.dataRelation.relationType === 'MAJOR_DROP', '应该识别为重大变化');
});

// 测试用例 24: 服务故障恢复模式
test.test('服务故障恢复模式', () => {
    const current = [100, 200, 500, 800, 950, 1000, 1020, 980, 1030, 990];
    const yoy = [1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000];

    const result = detectRiskFromWindows(current, yoy);

    test.assertRiskAbove(result.risk, 2, '故障恢复过程应该产生一定风险');
    test.assert(result.risk > 0, '应该检测到风险');
});

// 测试用例 25: 周期性流量波动 (业务高峰)
test.test('周期性流量波动 (业务高峰)', () => {
    const current = [800, 1200, 800, 1200, 800, 1200, 800, 1200, 800, 1200];
    const yoy = [1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000];

    const result = detectRiskFromWindows(current, yoy);

    test.assertRiskAbove(result.risk, 0.5, '周期性波动应该产生一定风险');
    test.assert(result.perPoint.filter(p => p.isAnomaly).length >= 0, '可能检测到异常点');
});

// 测试用例 26: 缓慢流量泄露 (内存泄露等)
test.test('缓慢流量泄露', () => {
    const current = [1000, 950, 900, 850, 800, 750, 700, 650, 600, 550];
    const yoy = [1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000];

    const result = detectRiskFromWindows(current, yoy);

    test.assertRiskAbove(result.risk, 8, '缓慢下降应该产生一定风险');
    test.assert(result.dataRelation.flowChangePercent < -20, '应该检测到显著下降');
});

// 测试用例 27: 流量断崖式下跌 (服务宕机)
test.test('流量断崖式下跌 (服务宕机)', () => {
    const current = [1000, 1020, 980, 50, 0, 0, 0, 0, 0, 0];
    const yoy = [1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000];

    const result = detectRiskFromWindows(current, yoy);

    test.assertRiskAbove(result.risk, 70, '服务宕机应该是极高风险');
    test.assert(result.consCount >= 3, '应该检测到连续异常');
});

// 测试用例 28: 流量雪崩效应
test.test('流量雪崩效应', () => {
    const current = [1000, 2000, 4000, 8000, 16000, 32000, 25000, 20000, 15000, 10000];
    const yoy = [1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000];

    const result = detectRiskFromWindows(current, yoy);

    test.assertRiskAbove(result.risk, 80, '雪崩效应应该是极高风险');
    test.assert(result.dataRelation.relationType === 'MAJOR_SURGE' || result.dataRelation.relationType === 'EXTREME_SURGE', '应该识别为重大或极端暴增');
});

// 测试用例 29: 数据不一致 (监控系统故障)
test.test('数据不一致 (监控系统故障)', () => {
    const current = [1000, -100, 999999, 0, 1000, NaN, 1000, Infinity, 1000, 1000];
    const yoy = [1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000];

    const result = detectRiskFromWindows(current, yoy);

    test.assert(result.risk >= 0, '应该能处理异常数据');
    test.assert(result.confidence >= 0, '应该返回有效的置信度');
});

// 测试用例 30: 微服务级联故障
test.test('微服务级联故障', () => {
    const current = [1000, 800, 600, 400, 200, 100, 50, 25, 10, 5];
    const yoy = [1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000];

    const result = detectRiskFromWindows(current, yoy);

    test.assertRiskAbove(result.risk, 35, '级联故障应该是中高风险');
    test.assert(result.dataRelation.flowChangePercent < -80, '应该检测到严重下降');
});

// 测试用例 31: 流量突发后稳定 (营销活动)
test.test('流量突发后稳定 (营销活动)', () => {
    const current = [1000, 1020, 3000, 3100, 2950, 3050, 2980, 3020, 2990, 3000];
    const yoy = [1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000];

    const result = detectRiskFromWindows(current, yoy);

    test.assertRiskAbove(result.risk, 80, '营销活动流量突增应该是高风险');
    test.assert(result.EP >= 0, '可能检测到持续性异常');
});

// 测试用例 32: 地域性流量异常 (网络分区)
test.test('地域性流量异常 (网络分区)', () => {
    const current = [1000, 1020, 500, 520, 480, 510, 490, 505, 495, 500];
    const yoy = [1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000];

    const result = detectRiskFromWindows(current, yoy);

    test.assertRiskAbove(result.risk, 10, '网络分区应该产生一定风险');
    test.assert(result.dataRelation.flowChangePercent < -40, '应该检测到流量下降');
});

// 测试用例 33: 同比数据有波动 - 当前数据正常
test.test('同比数据有波动 - 当前数据正常', () => {
    const current = [1000, 1020, 980, 1030, 990, 1050, 1010, 970, 1040, 1000];
    const yoy = [800, 1200, 600, 1400, 500, 1500, 400, 1600, 300, 1700];

    const result = detectRiskFromWindows(current, yoy);

    test.assertRiskBelow(result.risk, 15, '同比波动大但当前稳定应该是低风险');
    test.assert(result.dataRelation.relationType === 'TRAFFIC_MONITORING', '应该识别为流量监控类型');
});

// 测试用例 34: 双向波动 - 同比和当前都有波动
test.test('双向波动 - 同比和当前都有波动', () => {
    const current = [800, 1200, 600, 1400, 500, 1500, 400, 1600, 300, 1700];
    const yoy = [900, 1100, 700, 1300, 600, 1400, 500, 1500, 400, 1600];

    const result = detectRiskFromWindows(current, yoy);

    test.assertRiskBelow(result.risk, 5, '双向波动但整体平衡应该是低风险');
    test.assert(Math.abs(result.dataRelation.flowChangePercent) < 5, '整体变化应该很小');
});

// 测试用例 35: 波动趋势一致 - 同步增长
test.test('波动趋势一致 - 同步增长', () => {
    const current = [1100, 1150, 1080, 1180, 1120, 1220, 1160, 1260, 1200, 1300];
    const yoy = [1000, 1050, 980, 1080, 1020, 1120, 1060, 1160, 1100, 1200];

    const result = detectRiskFromWindows(current, yoy);

    test.assertRiskBelow(result.risk, 10, '同步增长趋势应该是低风险');
    test.assert(Math.abs(result.dataRelation.flowChangePercent) < 15, '整体变化应该在合理范围');
});

// 测试用例 36: 波动趋势相反 - 逆向变化
test.test('波动趋势相反 - 逆向变化', () => {
    const current = [1200, 1150, 1300, 1100, 1350, 1050, 1400, 1000, 1450, 950];
    const yoy = [800, 850, 700, 900, 650, 950, 600, 1000, 550, 1050];

    const result = detectRiskFromWindows(current, yoy);

    test.assertRiskAbove(result.risk, 10, '逆向趋势变化应该产生一定风险');
    test.assert(result.dataRelation.flowChangePercent > 20, '应该检测到显著变化');
});

// 测试用例 37: 量级差异 + 波动 - 高流量vs低流量波动
test.test('量级差异 + 波动 - 高流量vs低流量波动', () => {
    const current = [100000, 120000, 80000, 140000, 60000, 160000, 40000, 180000, 20000, 200000];
    const yoy = [80, 120, 60, 140, 50, 150, 40, 160, 30, 170];

    const result = detectRiskFromWindows(current, yoy);

    test.assertRiskAbove(result.risk, 70, '量级差异巨大应该是高风险');
    test.assert(result.dataRelation.relationType === 'EXTREME_SURGE' || result.dataRelation.relationType === 'MAJOR_SURGE', '应该识别为重大变化');
});

// 测试用例 38: 波动幅度对比 - 当前波动更大
test.test('波动幅度对比 - 当前波动更大', () => {
    const current = [500, 1500, 200, 1800, 100, 1900, 50, 1950, 25, 1975];
    const yoy = [950, 1050, 980, 1020, 990, 1010, 995, 1005, 998, 1002];

    const result = detectRiskFromWindows(current, yoy);

    test.assertRiskAbove(result.risk, 2, '当前波动远大于同比应该产生一定风险');
    test.assert(result.perPoint.filter(p => p.isAnomaly).length >= 5, '应该检测到多个异常点');
});

// 测试用例 39: 波动幅度对比 - 同比波动更大
test.test('波动幅度对比 - 同比波动更大', () => {
    const current = [950, 1050, 980, 1020, 990, 1010, 995, 1005, 998, 1002];
    const yoy = [500, 1500, 200, 1800, 100, 1900, 50, 1950, 25, 1975];

    const result = detectRiskFromWindows(current, yoy);

    test.assertRiskBelow(result.risk, 20, '同比波动大但当前稳定应该是低风险');
    test.assert(result.confidence > 0.5, '稳定的当前数据应该有较高置信度');
});

// 测试用例 40: 相位差波动 - 错位波动
test.test('相位差波动 - 错位波动', () => {
    const current = [1000, 1100, 1000, 1100, 1000, 1100, 1000, 1100, 1000, 1100];
    const yoy = [1100, 1000, 1100, 1000, 1100, 1000, 1100, 1000, 1100, 1000];

    const result = detectRiskFromWindows(current, yoy);

    test.assertRiskInRange(result.risk, 1, 15, '相位差波动应该产生轻微风险');
    test.assert(result.perPoint.filter(p => p.isAnomaly).length >= 0, '可能检测到异常点');
});

// 测试用例 41: 渐进式偏离 - 缓慢脱离同比趋势
test.test('渐进式偏离 - 缓慢脱离同比趋势', () => {
    const current = [1000, 1020, 1050, 1090, 1140, 1200, 1270, 1350, 1440, 1540];
    const yoy = [1000, 1010, 1020, 1030, 1040, 1050, 1060, 1070, 1080, 1090];

    const result = detectRiskFromWindows(current, yoy);

    test.assertRiskAbove(result.risk, 3, '渐进式偏离应该产生一定风险');
    test.assert(result.dataRelation.flowChangePercent > 10, '应该检测到增长趋势');
});

// 测试用例 42: 收敛波动 - 从波动到稳定
test.test('收敛波动 - 从波动到稳定', () => {
    const current = [800, 1200, 900, 1100, 950, 1050, 980, 1020, 995, 1005];
    const yoy = [1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000];

    const result = detectRiskFromWindows(current, yoy);

    test.assertRiskBelow(result.risk, 10, '收敛到稳定应该是低风险');
    test.assert(Math.abs(result.dataRelation.flowChangePercent) < 5, '整体变化应该很小');
});

// 测试用例 43: 发散波动 - 从稳定到波动
test.test('发散波动 - 从稳定到波动', () => {
    const current = [995, 1005, 980, 1020, 950, 1050, 900, 1100, 800, 1200];
    const yoy = [1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000];

    const result = detectRiskFromWindows(current, yoy);

    test.assertRiskAbove(result.risk, 0.5, '发散波动应该产生一定风险');
    test.assert(result.perPoint.filter(p => p.isAnomaly).length >= 0, '可能检测到异常');
});

// 测试用例 44: 多频率波动 - 复杂波动模式
test.test('多频率波动 - 复杂波动模式', () => {
    const current = [1000, 1100, 950, 1150, 900, 1200, 850, 1250, 800, 1300];
    const yoy = [1050, 1000, 1100, 950, 1150, 900, 1200, 850, 1250, 800];

    const result = detectRiskFromWindows(current, yoy);

    test.assertRiskAbove(result.risk, 0.5, '复杂波动应该产生一定风险');
    test.assert(result.risk > 0, '复杂模式应该被检测到');
});

// 运行所有测试
if (require.main === module) {
    test.run().then(success => {
        process.exit(success ? 0 : 1);
    });
}

module.exports = { test, detectRiskFromWindows };
