# 风险检测算法对比分析

本文档详细对比了两个风险检测算法的设计理念、适用场景和性能表现。

## 算法概览

### 1. 流量风险检测算法 (`risk-detector.js`)
- **设计目标**: 检测服务请求量、QPS等流量指标的异常变化
- **异常模式**: 双向检测（陡增和骤降都视为异常）
- **核心算法**: 鲁棒回归 + 自适应阈值 + 多维度评分

### 2. 响应耗时风险检测算法 (`latency-risk-detector.js`)  
- **设计目标**: 检测接口响应时间、查询耗时等延迟指标的异常
- **异常模式**: 单向检测（只有耗时增加才是异常）
- **核心算法**: 多分位数联合分析 + 同比模式学习 + 业务影响评估

## 核心差异分析

### 1. 异常检测方向性

#### 流量监控：双向异常检测
```javascript
// 流量骤降也是异常（可能表示服务故障）
if (Math.abs(z) >= config.z_th || ratio >= config.ratio_th) {
    isAnomaly = true; // 无论正负都检测
}
```

#### 耗时监控：单向异常检测
```javascript
// 只有耗时增加才是异常（耗时下降是性能改善）
const isPositiveAnomaly = residual > 0 && (
    z >= config.z_th ||  // 移除Math.abs()
    ratio >= config.ratio_th
);
```

### 2. 数据维度处理

#### 流量监控：单一指标
- 输入：单一数值序列（如QPS数组）
- 处理：时间序列分析
- 输出：整体风险评分

#### 耗时监控：多分位数联合
- 输入：多分位数数据（AVG, P50, P75, P95, P99）
- 处理：分位数权重聚合
- 输出：分位数级别 + 整体风险评分

```javascript
// 耗时监控的分位数权重
const PERCENTILE_WEIGHTS = {
    '1': 0.1,      // 平均值 - 基础权重
    '0.5': 0.15,   // P50 - 中等权重  
    '0.75': 0.2,   // P75 - 较高权重
    '0.95': 0.25,  // P95 - 高权重（SLA关键指标）
    '0.99': 0.3    // P99 - 最高权重（用户体验关键）
};
```

### 3. 同比模式学习

#### 流量监控：规模自适应
- 重点：流量规模差异处理
- 策略：小流量服务降低敏感度
- 应用：自动调整检测阈值

#### 耗时监控：时间模式识别
- 重点：高峰时段模式识别
- 策略：学习同比数据的固有特征
- 应用：自适应基线调整

```javascript
// 耗时监控的高峰模式调整
if (yoyPattern.hasPeakPattern) {
    const peakAdjustment = 1 + (yoyPattern.peakIntensity - 1) * adjustmentStrength;
    adjustedBaseline *= peakAdjustment;
}
```

## 适用场景对比

### 流量监控适用场景
| 监控指标 | 异常特征 | 业务影响 |
|----------|----------|----------|
| API请求量 | 突增/骤降 | 服务可用性 |
| 数据库连接数 | 异常波动 | 资源耗尽 |
| 消息队列吞吐 | 积压/空闲 | 处理能力 |
| 系统并发数 | 超载/异常 | 性能瓶颈 |

### 耗时监控适用场景
| 监控指标 | 异常特征 | 业务影响 |
|----------|----------|----------|
| API响应时间 | P99尖刺 | 用户体验 |
| 数据库查询耗时 | 慢查询增多 | 系统性能 |
| 第三方调用延迟 | 超时增加 | 服务依赖 |
| 批处理执行时间 | 处理变慢 | 任务积压 |

## 算法性能对比

### 测试场景设计

#### 流量监控测试场景
1. **正常波动**: 10%以内的随机波动
2. **流量突增**: 3倍流量增长
3. **流量骤降**: 50%流量下降
4. **小流量服务**: QPS < 100的服务
5. **渐进式增长**: 缓慢但持续的增长
6. **周期性波动**: 有规律的高峰低谷

#### 耗时监控测试场景
1. **正常稳定**: 各分位数稳定
2. **P99突发尖刺**: P99短时间内大幅上升
3. **系统性劣化**: 所有分位数同步上升
4. **长尾恶化**: P99上升但平均值稳定
5. **高峰时段模式**: 同比也有高峰特征
6. **性能改善**: 各分位数下降

### 测试结果对比

| 算法 | 测试场景数 | 通过率 | 平均得分 | 主要优势 |
|------|------------|--------|----------|----------|
| 流量检测 | 6 | 83.3% | 85.2% | 小流量自适应，突变检测准确 |
| 耗时检测 | 6 | 50.0% | 72.2% | 多分位数分析，模式学习 |

### 性能分析

#### 流量检测算法优势
- ✅ **高准确率**: 对流量突变检测准确
- ✅ **自适应强**: 自动适应不同规模服务
- ✅ **误报率低**: 小流量服务保护机制
- ✅ **响应快速**: 能快速识别异常模式

#### 耗时检测算法优势
- ✅ **维度丰富**: 多分位数提供全面视角
- ✅ **业务导向**: 从用户体验角度评估风险
- ✅ **模式学习**: 能适应业务高峰特征
- ✅ **告警智能**: 减少高峰期误报

#### 待优化点

##### 流量检测算法
- 🔄 **复杂模式**: 对复杂周期性模式的识别
- 🔄 **预测能力**: 缺乏趋势预测功能

##### 耗时检测算法  
- 🔄 **敏感度调优**: 需要进一步优化检测敏感度
- 🔄 **计算复杂度**: 多分位数处理增加计算开销

## 选择建议

### 选择流量检测算法的情况
- ✅ 监控对象是计数类指标（QPS、连接数、消息数）
- ✅ 关注服务可用性和资源利用率
- ✅ 需要检测流量异常波动（增加或减少）
- ✅ 服务规模差异较大，需要自适应调节

### 选择耗时检测算法的情况
- ✅ 监控对象是延迟类指标（响应时间、查询耗时）
- ✅ 关注用户体验和SLA达成
- ✅ 有多分位数数据可用
- ✅ 业务有明显的高峰低谷模式

### 混合使用策略
对于复杂的监控场景，可以同时使用两个算法：

```javascript
// 同时监控流量和耗时
const trafficRisk = detectRiskFromWindows(trafficData, trafficYoy);
const latencyRisk = detectLatencyRiskFromWindows(latencyData, latencyYoy);

// 综合评估
const overallRisk = Math.max(trafficRisk.risk, latencyRisk.risk);
const alerts = [...trafficRisk.alerts, ...latencyRisk.alerts];
```

## 未来发展方向

### 算法融合
- 🚀 **统一框架**: 开发统一的风险检测框架
- 🚀 **自动选择**: 根据数据特征自动选择算法
- 🚀 **结果融合**: 智能融合多个算法的结果

### 功能增强
- 🚀 **预测能力**: 增加趋势预测和早期预警
- 🚀 **根因分析**: 提供异常根因分析建议
- 🚀 **自动调优**: 基于反馈自动优化参数

### 工程优化
- 🚀 **性能优化**: 降低计算复杂度，提高处理速度
- 🚀 **可视化**: 提供丰富的可视化分析界面
- 🚀 **集成便利**: 简化集成和配置流程

## 总结

两个算法各有特色，针对不同的监控场景进行了专门优化：

- **流量检测算法**更适合传统的服务监控场景，重点关注服务可用性
- **耗时检测算法**更适合现代微服务架构，重点关注用户体验

选择合适的算法需要根据具体的监控需求、数据特征和业务场景来决定。在实际应用中，建议根据监控对象的特性选择对应的算法，或者在关键服务上同时使用两个算法以获得更全面的风险评估。
