/**
 * 外网依赖场景调试脚本
 * 专门测试和调试外网依赖模式检测功能
 */

const { detectLatencyRiskFromWindows } = require('./latency-risk-detector');

// 生成外网依赖场景的测试数据
function generateExternalDependencyData() {
    const baseTime = 1757179920;
    const times = Array.from({length: 30}, (_, i) => baseTime + i * 60);
    
    const baseLatencies = {
        '1': 0.02,      // 平均20ms
        '0.5': 0.015,   // P50: 15ms  
        '0.75': 0.025,  // P75: 25ms
        '0.95': 0.05,   // P95: 50ms
        '0.99': 0.1     // P99: 100ms
    };

    // 当前数据：外网依赖导致P99/P95上升
    const currentData = [];
    times.forEach(time => {
        Object.entries(baseLatencies).forEach(([category, baseValue]) => {
            let value = baseValue;
            
            // 外网依赖影响：主要影响高分位数
            if (category === '0.99') {
                value = baseValue * 5; // P99上升5倍
            } else if (category === '0.95') {
                value = baseValue * 3; // P95上升3倍
            } else if (category === '0.75') {
                value = baseValue * 1.2; // P75轻微影响
            }
            // P50和AVG基本不受影响
            
            // 添加一些随机噪声
            const noise = 1 + (Math.random() - 0.5) * 0.1;
            value *= noise;
            
            currentData.push({
                time: time,
                value: Math.max(0.001, value).toFixed(6),
                category: category
            });
        });
    });

    // 同比数据：也有类似的外网依赖模式（但程度稍轻）
    const yoyData = [];
    times.forEach(time => {
        Object.entries(baseLatencies).forEach(([category, baseValue]) => {
            let value = baseValue;
            
            // 同比也有外网依赖影响，但程度稍轻
            if (category === '0.99') {
                value = baseValue * 4; // P99上升4倍
            } else if (category === '0.95') {
                value = baseValue * 2.5; // P95上升2.5倍
            } else if (category === '0.75') {
                value = baseValue * 1.1; // P75轻微影响
            }
            
            const noise = 1 + (Math.random() - 0.5) * 0.1;
            value *= noise;
            
            yoyData.push({
                time: time,
                value: Math.max(0.001, value).toFixed(6),
                category: category
            });
        });
    });

    return { currentData, yoyData };
}

// 生成对比场景：真正的系统异常
function generateSystemAnomalyData() {
    const baseTime = 1757179920;
    const times = Array.from({length: 30}, (_, i) => baseTime + i * 60);
    
    const baseLatencies = {
        '1': 0.02,
        '0.5': 0.015,
        '0.75': 0.025,
        '0.95': 0.05,
        '0.99': 0.1
    };

    // 系统异常：所有分位数都上升
    const currentData = [];
    times.forEach(time => {
        Object.entries(baseLatencies).forEach(([category, baseValue]) => {
            const multiplier = 3; // 所有分位数都上升3倍
            const value = baseValue * multiplier;
            
            currentData.push({
                time: time,
                value: Math.max(0.001, value).toFixed(6),
                category: category
            });
        });
    });

    // 同比数据：正常
    const yoyData = [];
    times.forEach(time => {
        Object.entries(baseLatencies).forEach(([category, baseValue]) => {
            yoyData.push({
                time: time,
                value: baseValue.toFixed(6),
                category: category
            });
        });
    });

    return { currentData, yoyData };
}

// 分析和显示结果
function analyzeScenario(name, currentData, yoyData) {
    console.log(`\n=== ${name} ===`);
    
    const result = detectLatencyRiskFromWindows(currentData, yoyData, {
        serviceType: 'MICROSERVICE',
        enablePatternLearning: true
    });

    // 基础指标
    console.log(`风险评分: ${(result.risk * 100).toFixed(1)}%`);
    console.log(`置信度: ${(result.confidence * 100).toFixed(1)}%`);
    console.log(`告警数量: ${result.alerts.length}`);

    // 分位数详情
    console.log('\n分位数分析:');
    Object.entries(result.percentileRisks).forEach(([percentile, risk]) => {
        const name = percentile === '1' ? 'AVG' : 
                    percentile === '0.5' ? 'P50' :
                    percentile === '0.75' ? 'P75' :
                    percentile === '0.95' ? 'P95' : 'P99';
        
        console.log(`  ${name}: ${(risk.currentValue * 1000).toFixed(1)}ms (风险: ${(risk.combinedRisk * 100).toFixed(1)}%)`);
    });

    // 外网依赖检测结果
    if (result.externalDependencyPattern) {
        const pattern = result.externalDependencyPattern;
        console.log('\n外网依赖检测:');
        console.log(`  检测结果: ${pattern.isExternalDependency ? '✅ 检测到外网依赖' : '❌ 未检测到外网依赖'}`);
        console.log(`  置信度: ${(pattern.confidence * 100).toFixed(1)}%`);
        console.log(`  P99/P50比值: ${pattern.p99_p50_ratio.toFixed(1)}`);
        console.log(`  P95/P50比值: ${pattern.p95_p50_ratio.toFixed(1)}`);
        console.log(`  低分位数稳定: ${pattern.lowPercentileStable ? '是' : '否'}`);
        console.log(`  高分位数突刺: ${pattern.highPercentileSpike ? '是' : '否'}`);
        console.log(`  证据: ${pattern.evidence.join(', ')}`);
    }

    // 业务影响
    console.log('\n业务影响评估:');
    console.log(`  用户体验: ${result.businessImpact.userExperience.level}`);
    console.log(`  系统稳定性: ${result.businessImpact.systemStability.level}`);
    console.log(`  业务连续性: ${result.businessImpact.businessContinuity.level}`);

    // 告警详情
    if (result.alerts.length > 0) {
        console.log('\n告警详情:');
        result.alerts.forEach((alert, index) => {
            console.log(`  ${index + 1}. [${alert.level}] ${alert.type}: ${alert.message}`);
            if (alert.type === 'EXTERNAL_DEPENDENCY_IMPACT') {
                console.log(`     推荐措施: ${alert.recommendation}`);
            }
        });
    } else {
        console.log('\n告警详情: 无告警');
    }

    return result;
}

// 主测试函数
function debugExternalDependency() {
    console.log('🔍 外网依赖场景调试分析');
    console.log('==========================================');

    // 场景1：外网依赖场景
    const externalDep = generateExternalDependencyData();
    const externalResult = analyzeScenario('外网依赖场景', externalDep.currentData, externalDep.yoyData);

    // 场景2：系统异常场景（对比）
    const systemAnomaly = generateSystemAnomalyData();
    const anomalyResult = analyzeScenario('系统异常场景（对比）', systemAnomaly.currentData, systemAnomaly.yoyData);

    // 对比分析
    console.log('\n=== 对比分析 ===');
    console.log('外网依赖 vs 系统异常:');
    console.log(`风险评分: ${(externalResult.risk * 100).toFixed(1)}% vs ${(anomalyResult.risk * 100).toFixed(1)}%`);
    console.log(`告警数量: ${externalResult.alerts.length} vs ${anomalyResult.alerts.length}`);
    
    const externalPattern = externalResult.externalDependencyPattern;
    const anomalyPattern = anomalyResult.externalDependencyPattern;
    
    console.log(`外网依赖检测: ${externalPattern?.isExternalDependency ? '✅' : '❌'} vs ${anomalyPattern?.isExternalDependency ? '✅' : '❌'}`);
    
    // 验证算法效果
    console.log('\n=== 算法效果验证 ===');
    
    const isExternalDepCorrect = externalPattern?.isExternalDependency === true;
    const isAnomalyCorrect = anomalyPattern?.isExternalDependency === false;
    const isRiskReasonable = externalResult.risk < anomalyResult.risk; // 外网依赖风险应该低于系统异常
    
    console.log(`外网依赖检测准确性: ${isExternalDepCorrect ? '✅ 正确' : '❌ 错误'}`);
    console.log(`系统异常区分准确性: ${isAnomalyCorrect ? '✅ 正确' : '❌ 错误'}`);
    console.log(`风险评分合理性: ${isRiskReasonable ? '✅ 合理' : '❌ 不合理'}`);
    
    const overallSuccess = isExternalDepCorrect && isAnomalyCorrect && isRiskReasonable;
    console.log(`\n总体评估: ${overallSuccess ? '🎉 算法工作正常' : '⚠️ 算法需要调优'}`);

    if (!overallSuccess) {
        console.log('\n调优建议:');
        if (!isExternalDepCorrect) {
            console.log('- 调整外网依赖检测阈值，提高检测敏感度');
        }
        if (!isAnomalyCorrect) {
            console.log('- 优化外网依赖检测逻辑，避免误判系统异常');
        }
        if (!isRiskReasonable) {
            console.log('- 调整外网依赖场景的风险折扣系数');
        }
    }

    return {
        externalResult,
        anomalyResult,
        success: overallSuccess
    };
}

// 如果直接运行此文件，执行调试
if (require.main === module) {
    debugExternalDependency();
}

module.exports = {
    generateExternalDependencyData,
    generateSystemAnomalyData,
    analyzeScenario,
    debugExternalDependency
};
