<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数值格式化测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-card {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.2rem;
            font-weight: bold;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.9rem;
            font-weight: bold;
        }
        .fixed {
            background: #d4edda;
            color: #155724;
        }
        .test-demo {
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #007bff;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 15px 0;
        }
        .before, .after {
            padding: 10px;
            border-radius: 4px;
        }
        .before {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
        }
        .after {
            background: #d4edda;
            border-left: 4px solid #28a745;
        }
        .number-example {
            font-family: monospace;
            font-weight: bold;
            color: #495057;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background: #f8f9fa;
            font-weight: bold;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
    </style>
</head>
<body>
    <h1>数值格式化修复验证</h1>
    
    <div class="test-card">
        <div class="test-title">修复概述 <span class="status fixed">✓ 已完成</span></div>
        <p>所有视图上显示的数值指标现在统一保持最多2位小数，提供一致的用户体验。</p>
        <div class="test-demo">
            <strong>修复范围：</strong>
            <ul>
                <li>风险概率显示</li>
                <li>置信度显示</li>
                <li>关键指标 (S, M, P, EP)</li>
                <li>数据点表格中的数值</li>
                <li>自适应信息表格</li>
                <li>趋势分析数据</li>
                <li>告警信息中的数值</li>
            </ul>
        </div>
    </div>

    <div class="test-card">
        <div class="test-title">主要指标格式化对比</div>
        <div class="comparison">
            <div class="before">
                <h4>修复前</h4>
                <div class="number-example">风险概率: 67.8%</div>
                <div class="number-example">置信度: 85%</div>
                <div class="number-example">严重性: 3</div>
                <div class="number-example">比率阈值: 0.120</div>
            </div>
            <div class="after">
                <h4>修复后</h4>
                <div class="number-example">风险概率: 67.80%</div>
                <div class="number-example">置信度: 85.00%</div>
                <div class="number-example">严重性: 3.00</div>
                <div class="number-example">比率阈值: 0.12</div>
            </div>
        </div>
    </div>

    <div class="test-card">
        <div class="test-title">格式化函数测试</div>
        <div class="test-demo">
            <p>新增了两个统一的格式化函数：</p>
            <ul>
                <li><code>formatNumber(value, decimals = 2)</code> - 格式化普通数值</li>
                <li><code>formatPercentage(value, decimals = 2)</code> - 格式化百分比</li>
            </ul>
            
            <h4>测试用例：</h4>
            <table>
                <tr>
                    <th>输入值</th>
                    <th>formatNumber()</th>
                    <th>formatPercentage()</th>
                </tr>
                <tr>
                    <td>0.12345</td>
                    <td id="test1-num">-</td>
                    <td id="test1-pct">-</td>
                </tr>
                <tr>
                    <td>3.14159</td>
                    <td id="test2-num">-</td>
                    <td id="test2-pct">-</td>
                </tr>
                <tr>
                    <td>0.678</td>
                    <td id="test3-num">-</td>
                    <td id="test3-pct">-</td>
                </tr>
                <tr>
                    <td>null</td>
                    <td id="test4-num">-</td>
                    <td id="test4-pct">-</td>
                </tr>
            </table>
            
            <button class="test-button" onclick="runFormatTests()">运行格式化测试</button>
        </div>
    </div>

    <div class="test-card">
        <div class="test-title">具体修复项目</div>
        <div class="test-demo">
            <h4>✅ 已修复的显示项目：</h4>
            <ol>
                <li><strong>风险概率</strong>: 从1位小数改为2位小数</li>
                <li><strong>置信度</strong>: 从整数改为2位小数</li>
                <li><strong>关键指标</strong>: S, M, P, EP 统一显示2位小数</li>
                <li><strong>数据点表格</strong>: 当前值、同比值、基线、残差、Z分数</li>
                <li><strong>自适应信息</strong>: 所有统计数据和调节系数</li>
                <li><strong>趋势分析</strong>: 偏离程度、比例等百分比数据</li>
                <li><strong>告警信息</strong>: 置信度显示</li>
                <li><strong>图表工具提示</strong>: 保持2位小数显示</li>
            </ol>
        </div>
    </div>

    <div class="test-card">
        <div class="test-title">测试验证</div>
        <div style="text-align: center;">
            <button class="test-button" onclick="window.open('index.html', '_blank')">
                打开主页面验证格式化效果
            </button>
            <button class="test-button" onclick="showTestInstructions()">
                查看测试说明
            </button>
        </div>
    </div>

    <script>
        // 复制主页面的格式化函数进行测试
        function formatNumber(value, decimals = 2) {
            if (value === null || value === undefined || !Number.isFinite(Number(value))) {
                return '-';
            }
            return Number(value).toFixed(decimals);
        }

        function formatPercentage(value, decimals = 2) {
            if (value === null || value === undefined || !Number.isFinite(Number(value))) {
                return '-';
            }
            return (Number(value) * 100).toFixed(decimals) + '%';
        }

        function runFormatTests() {
            const testCases = [
                { value: 0.12345, id: 'test1' },
                { value: 3.14159, id: 'test2' },
                { value: 0.678, id: 'test3' },
                { value: null, id: 'test4' }
            ];

            testCases.forEach(test => {
                document.getElementById(test.id + '-num').textContent = formatNumber(test.value);
                document.getElementById(test.id + '-pct').textContent = formatPercentage(test.value);
            });
        }

        function showTestInstructions() {
            alert(`测试说明：

1. 打开主页面
2. 加载任意示例数据
3. 点击"分析风险"
4. 检查以下项目的数值格式：
   - 右侧风险评估面板的风险概率和置信度
   - 关键指标 S, M, P, EP 的显示
   - 数据点详情表格中的数值
   - 自适应信息和趋势分析中的数值
   - 智能告警中的数值

所有数值应该统一显示最多2位小数。`);
        }

        // 页面加载时自动运行测试
        window.addEventListener('load', function() {
            runFormatTests();
            console.log('数值格式化测试页面加载完成');
            console.log('所有数值现在统一显示最多2位小数');
        });
    </script>
</body>
</html>
