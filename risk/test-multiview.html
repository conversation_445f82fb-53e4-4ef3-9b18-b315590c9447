<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多视图功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            font-family: monospace;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 多视图功能测试</h1>
        <p>测试新增的数据对比可视化功能</p>

        <div class="test-section">
            <h3>📊 数据解析测试</h3>
            <div id="parseTest" class="test-result">等待测试...</div>
        </div>

        <div class="test-section">
            <h3>🔄 视图模式切换测试</h3>
            <div id="viewModeTest" class="test-result">等待测试...</div>
        </div>

        <div class="test-section">
            <h3>📈 差值计算测试</h3>
            <div id="diffTest" class="test-result">等待测试...</div>
        </div>

        <div class="test-section">
            <h3>🎯 数据集创建测试</h3>
            <div id="datasetTest" class="test-result">等待测试...</div>
        </div>

        <div class="test-section">
            <h3>📋 测试总结</h3>
            <div id="summary" class="test-result">等待测试完成...</div>
        </div>
    </div>

    <script src="latency-risk-detector.js"></script>
    <script>
        // 测试数据
        const testData = {
            current: [
                {time: 1757179920, value: "0.025", category: "1"},
                {time: 1757179920, value: "0.055", category: "0.95"},
                {time: 1757179920, value: "0.120", category: "0.99"},
                {time: 1757179980, value: "0.030", category: "1"},
                {time: 1757179980, value: "0.060", category: "0.95"},
                {time: 1757179980, value: "0.130", category: "0.99"}
            ],
            yoy: [
                {time: 1757179920, value: "0.020", category: "1"},
                {time: 1757179920, value: "0.050", category: "0.95"},
                {time: 1757179920, value: "0.100", category: "0.99"},
                {time: 1757179980, value: "0.022", category: "1"},
                {time: 1757179980, value: "0.052", category: "0.95"},
                {time: 1757179980, value: "0.105", category: "0.99"}
            ]
        };

        // 模拟多视图页面的核心函数
        function groupDataByPercentile(data) {
            const grouped = {};
            const timeLabels = new Set();
            
            data.forEach(item => {
                const percentile = item.category;
                if (!grouped[percentile]) {
                    grouped[percentile] = [];
                }
                grouped[percentile].push({
                    time: item.time,
                    value: parseFloat(item.value)
                });
                timeLabels.add(item.time);
            });
            
            Object.keys(grouped).forEach(percentile => {
                grouped[percentile].sort((a, b) => a.time - b.time);
            });
            
            return { grouped, timeLabels: Array.from(timeLabels).sort((a, b) => a - b) };
        }

        function calculateDifferenceData(current, yoy) {
            const result = {};
            
            Object.keys(current).forEach(percentile => {
                if (yoy[percentile]) {
                    const currentValues = current[percentile];
                    const yoyValues = yoy[percentile];
                    
                    result[percentile] = currentValues.map((curr, index) => {
                        const yoyVal = yoyValues[index];
                        if (yoyVal && yoyVal.value > 0) {
                            const diff = ((curr.value - yoyVal.value) / yoyVal.value) * 100;
                            return {
                                time: curr.time,
                                value: diff,
                                isImprovement: diff < 0,
                                isDegradation: diff > 20
                            };
                        }
                        return { time: curr.time, value: 0 };
                    });
                }
            });
            
            return result;
        }

        function createDataset(label, data, color, style) {
            return {
                label: label,
                data: data,
                borderColor: color,
                backgroundColor: style.backgroundColor || color + '20',
                borderWidth: style.borderWidth,
                tension: style.tension,
                fill: style.fill,
                borderDash: style.borderDash || []
            };
        }

        // 执行测试
        function runTests() {
            let passedTests = 0;
            let totalTests = 4;

            // 测试1: 数据解析
            try {
                const { grouped: currentGrouped, timeLabels } = groupDataByPercentile(testData.current);
                const { grouped: yoyGrouped } = groupDataByPercentile(testData.yoy);
                
                const hasCorrectStructure = 
                    currentGrouped['1'] && currentGrouped['0.95'] && currentGrouped['0.99'] &&
                    yoyGrouped['1'] && yoyGrouped['0.95'] && yoyGrouped['0.99'] &&
                    timeLabels.length === 2;

                if (hasCorrectStructure) {
                    document.getElementById('parseTest').innerHTML = 
                        '<span class="success">✅ 数据解析成功</span><br>' +
                        `解析到 ${Object.keys(currentGrouped).length} 个分位数，${timeLabels.length} 个时间点`;
                    passedTests++;
                } else {
                    throw new Error('数据结构不正确');
                }
            } catch (error) {
                document.getElementById('parseTest').innerHTML = 
                    `<span class="error">❌ 数据解析失败: ${error.message}</span>`;
            }

            // 测试2: 视图模式
            try {
                const { grouped: currentGrouped } = groupDataByPercentile(testData.current);
                const { grouped: yoyGrouped } = groupDataByPercentile(testData.yoy);
                
                const viewModes = ['current', 'yoy', 'comparison'];
                const results = [];
                
                viewModes.forEach(mode => {
                    let datasetCount = 0;
                    if (mode === 'current' || mode === 'comparison') {
                        datasetCount += Object.keys(currentGrouped).length;
                    }
                    if (mode === 'yoy' || mode === 'comparison') {
                        datasetCount += Object.keys(yoyGrouped).length;
                    }
                    results.push(`${mode}: ${datasetCount}个数据集`);
                });

                document.getElementById('viewModeTest').innerHTML = 
                    '<span class="success">✅ 视图模式切换正常</span><br>' +
                    results.join('<br>');
                passedTests++;
            } catch (error) {
                document.getElementById('viewModeTest').innerHTML = 
                    `<span class="error">❌ 视图模式测试失败: ${error.message}</span>`;
            }

            // 测试3: 差值计算
            try {
                const { grouped: currentGrouped } = groupDataByPercentile(testData.current);
                const { grouped: yoyGrouped } = groupDataByPercentile(testData.yoy);
                const diffData = calculateDifferenceData(currentGrouped, yoyGrouped);
                
                const p99Diff = diffData['0.99'];
                const expectedDiff1 = ((0.120 - 0.100) / 0.100) * 100; // 20%
                const expectedDiff2 = ((0.130 - 0.105) / 0.105) * 100; // ~23.8%
                
                const actualDiff1 = p99Diff[0].value;
                const actualDiff2 = p99Diff[1].value;
                
                const isCorrect = Math.abs(actualDiff1 - expectedDiff1) < 0.1 && 
                                 Math.abs(actualDiff2 - expectedDiff2) < 0.1;

                if (isCorrect) {
                    document.getElementById('diffTest').innerHTML = 
                        '<span class="success">✅ 差值计算正确</span><br>' +
                        `P99差值: ${actualDiff1.toFixed(1)}%, ${actualDiff2.toFixed(1)}%`;
                    passedTests++;
                } else {
                    throw new Error(`计算结果不正确: ${actualDiff1}, ${actualDiff2}`);
                }
            } catch (error) {
                document.getElementById('diffTest').innerHTML = 
                    `<span class="error">❌ 差值计算失败: ${error.message}</span>`;
            }

            // 测试4: 数据集创建
            try {
                const testDataset = createDataset(
                    'P99 (当前)',
                    [0.120, 0.130],
                    '#e74c3c',
                    { borderWidth: 3, tension: 0.3, fill: false }
                );
                
                const hasRequiredFields = testDataset.label && testDataset.data && 
                                         testDataset.borderColor && testDataset.borderWidth;

                if (hasRequiredFields) {
                    document.getElementById('datasetTest').innerHTML = 
                        '<span class="success">✅ 数据集创建成功</span><br>' +
                        `标签: ${testDataset.label}, 数据点: ${testDataset.data.length}`;
                    passedTests++;
                } else {
                    throw new Error('数据集字段不完整');
                }
            } catch (error) {
                document.getElementById('datasetTest').innerHTML = 
                    `<span class="error">❌ 数据集创建失败: ${error.message}</span>`;
            }

            // 测试总结
            const passRate = (passedTests / totalTests * 100).toFixed(1);
            const summaryClass = passedTests === totalTests ? 'success' : 
                               passedTests > totalTests / 2 ? 'info' : 'error';
            
            document.getElementById('summary').innerHTML = 
                `<span class="${summaryClass}">` +
                `📊 测试完成: ${passedTests}/${totalTests} 通过 (${passRate}%)` +
                '</span><br>' +
                (passedTests === totalTests ? 
                    '🎉 所有多视图功能测试通过！可以正常使用新的可视化功能。' :
                    '⚠️ 部分功能需要检查，请查看具体测试结果。');
        }

        // 页面加载后运行测试
        window.addEventListener('load', () => {
            setTimeout(runTests, 500);
        });
    </script>
</body>
</html>
