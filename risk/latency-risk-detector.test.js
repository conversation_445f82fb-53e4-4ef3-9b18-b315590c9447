/**
 * 响应耗时风险检测算法测试套件
 * 
 * 基于业界最佳实践设计的测试用例，覆盖各种真实场景：
 * 1. 正常场景：稳定耗时、性能改善
 * 2. 异常场景：突发尖刺、系统性劣化、长尾恶化
 * 3. 复杂场景：高峰时段、渐进式劣化、间歇性抖动
 * 4. 边界场景：极端值、数据缺失、同比模式异常
 */

const { detectLatencyRiskFromWindows, PERCENTILE_WEIGHTS, LATENCY_RISK_THRESHOLDS } = require('./latency-risk-detector');

// 测试数据生成工具
class LatencyTestDataGenerator {
    constructor() {
        this.baseTime = 1757179920;
        this.timeInterval = 60; // 1分钟间隔
    }

    // 生成基础时间序列
    generateTimePoints(count, startTime = this.baseTime) {
        return Array.from({ length: count }, (_, i) => startTime + i * this.timeInterval);
    }

    // 生成正常分布的耗时数据
    generateNormalLatency(times, baseLatencies, noise = 0.1) {
        return times.map((time, i) => {
            const data = [];
            Object.entries(baseLatencies).forEach(([percentile, baseValue]) => {
                const noiseValue = baseValue * (1 + (Math.random() - 0.5) * noise);
                data.push({
                    time: time,
                    value: Math.max(0.001, noiseValue).toFixed(6),
                    category: percentile
                });
            });
            return data;
        }).flat();
    }

    // 生成突发尖刺场景
    generateSpikeScenario(times, baseLatencies, spikeStart, spikeEnd, spikeMultiplier = 5) {
        return times.map((time, i) => {
            const data = [];
            const isInSpike = i >= spikeStart && i <= spikeEnd;
            
            Object.entries(baseLatencies).forEach(([percentile, baseValue]) => {
                let value = baseValue;
                if (isInSpike) {
                    // P99和P95受尖刺影响更大
                    const multiplier = percentile === '0.99' ? spikeMultiplier : 
                                     percentile === '0.95' ? spikeMultiplier * 0.7 :
                                     percentile === '0.75' ? spikeMultiplier * 0.3 :
                                     spikeMultiplier * 0.1;
                    value = baseValue * multiplier;
                }
                
                data.push({
                    time: time,
                    value: Math.max(0.001, value).toFixed(6),
                    category: percentile
                });
            });
            return data;
        }).flat();
    }

    // 生成系统性劣化场景
    generateSystemicDegradation(times, baseLatencies, degradationFactor = 2) {
        return times.map((time, i) => {
            const data = [];
            // 渐进式劣化
            const progressiveFactor = 1 + (degradationFactor - 1) * (i / times.length);
            
            Object.entries(baseLatencies).forEach(([percentile, baseValue]) => {
                const value = baseValue * progressiveFactor;
                data.push({
                    time: time,
                    value: Math.max(0.001, value).toFixed(6),
                    category: percentile
                });
            });
            return data;
        }).flat();
    }

    // 生成长尾恶化场景
    generateTailDegradation(times, baseLatencies, tailMultiplier = 3) {
        return times.map((time, i) => {
            const data = [];
            
            Object.entries(baseLatencies).forEach(([percentile, baseValue]) => {
                let value = baseValue;
                // 只有高分位数受影响
                if (percentile === '0.99') {
                    value = baseValue * tailMultiplier;
                } else if (percentile === '0.95') {
                    value = baseValue * (1 + (tailMultiplier - 1) * 0.5);
                }
                // 平均值和P50基本不变
                
                data.push({
                    time: time,
                    value: Math.max(0.001, value).toFixed(6),
                    category: percentile
                });
            });
            return data;
        }).flat();
    }

    // 生成高峰时段模式
    generatePeakPattern(times, baseLatencies, peakMultiplier = 1.8) {
        return times.map((time, i) => {
            const data = [];
            // 模拟高峰时段（中间时段）
            const peakStart = Math.floor(times.length * 0.3);
            const peakEnd = Math.floor(times.length * 0.7);
            const isInPeak = i >= peakStart && i <= peakEnd;
            
            Object.entries(baseLatencies).forEach(([percentile, baseValue]) => {
                let value = baseValue;
                if (isInPeak) {
                    // 高分位数在高峰期上升更明显
                    const multiplier = percentile === '0.99' ? peakMultiplier :
                                     percentile === '0.95' ? peakMultiplier * 0.8 :
                                     percentile === '0.75' ? peakMultiplier * 0.5 :
                                     peakMultiplier * 0.3;
                    value = baseValue * multiplier;
                }
                
                data.push({
                    time: time,
                    value: Math.max(0.001, value).toFixed(6),
                    category: percentile
                });
            });
            return data;
        }).flat();
    }

    // 生成性能改善场景
    generatePerformanceImprovement(times, baseLatencies, improvementFactor = 0.5) {
        return times.map((time, i) => {
            const data = [];
            // 渐进式改善
            const progressiveFactor = 1 - (1 - improvementFactor) * (i / times.length);
            
            Object.entries(baseLatencies).forEach(([percentile, baseValue]) => {
                const value = baseValue * progressiveFactor;
                data.push({
                    time: time,
                    value: Math.max(0.001, value).toFixed(6),
                    category: percentile
                });
            });
            return data;
        }).flat();
    }

    // 生成外网依赖场景数据：P99/P95上升但P50/P75稳定
    generateExternalDependencyScenario(times, baseLatencies, p99Multiplier = 5, p95Multiplier = 3) {
        return times.map((time, i) => {
            const data = [];

            Object.entries(baseLatencies).forEach(([percentile, baseValue]) => {
                let value = baseValue;

                // 外网依赖影响：主要影响高分位数
                if (percentile === '0.99') {
                    value = baseValue * p99Multiplier;
                } else if (percentile === '0.95') {
                    value = baseValue * p95Multiplier;
                } else if (percentile === '0.75') {
                    value = baseValue * 1.2; // 轻微影响
                }
                // P50和AVG基本不受影响

                // 添加一些随机噪声
                const noise = 1 + (Math.random() - 0.5) * 0.1;
                value *= noise;

                data.push({
                    time: time,
                    value: Math.max(0.001, value).toFixed(6),
                    category: percentile
                });
            });
            return data;
        }).flat();
    }
}

// 测试评分系统
class TestScorer {
    constructor() {
        this.testResults = [];
    }

    // 评估测试结果
    scoreTest(testName, result, expectedOutcome) {
        const score = this.calculateScore(result, expectedOutcome);
        this.testResults.push({
            testName,
            score,
            result,
            expectedOutcome,
            passed: score >= 0.7
        });
        return score;
    }

    calculateScore(result, expected) {
        let score = 0;
        let totalWeight = 0;

        // 风险评估准确性 (40%)
        const riskAccuracy = this.evaluateRiskAccuracy(result.risk, expected.riskRange);
        score += riskAccuracy * 0.4;
        totalWeight += 0.4;

        // 告警准确性 (30%)
        const alertAccuracy = this.evaluateAlertAccuracy(result.alerts, expected.expectedAlerts);
        score += alertAccuracy * 0.3;
        totalWeight += 0.3;

        // 业务影响评估准确性 (20%)
        const businessAccuracy = this.evaluateBusinessImpact(result.businessImpact, expected.expectedBusinessImpact);
        score += businessAccuracy * 0.2;
        totalWeight += 0.2;

        // 置信度合理性 (10%)
        const confidenceScore = this.evaluateConfidence(result.confidence, expected.expectedConfidenceRange);
        score += confidenceScore * 0.1;
        totalWeight += 0.1;

        return score / totalWeight;
    }

    evaluateRiskAccuracy(actualRisk, expectedRange) {
        if (actualRisk >= expectedRange.min && actualRisk <= expectedRange.max) {
            return 1.0;
        }
        
        const distance = Math.min(
            Math.abs(actualRisk - expectedRange.min),
            Math.abs(actualRisk - expectedRange.max)
        );
        
        return Math.max(0, 1 - distance * 2); // 距离越远分数越低
    }

    evaluateAlertAccuracy(actualAlerts, expectedAlerts) {
        if (!expectedAlerts || expectedAlerts.length === 0) {
            return actualAlerts.length === 0 ? 1.0 : 0.5;
        }

        let matchedAlerts = 0;
        expectedAlerts.forEach(expectedAlert => {
            const found = actualAlerts.find(alert => 
                alert.type === expectedAlert.type && 
                alert.level === expectedAlert.level
            );
            if (found) matchedAlerts++;
        });

        const precision = actualAlerts.length > 0 ? matchedAlerts / actualAlerts.length : 0;
        const recall = matchedAlerts / expectedAlerts.length;
        
        return precision * 0.6 + recall * 0.4; // 更重视精确度
    }

    evaluateBusinessImpact(actualImpact, expectedImpact) {
        if (!expectedImpact) return 0.5;

        let score = 0;
        let count = 0;

        if (expectedImpact.userExperience) {
            score += actualImpact.userExperience.level === expectedImpact.userExperience ? 1 : 0;
            count++;
        }

        if (expectedImpact.systemStability) {
            score += actualImpact.systemStability.level === expectedImpact.systemStability ? 1 : 0;
            count++;
        }

        if (expectedImpact.businessContinuity) {
            score += actualImpact.businessContinuity.level === expectedImpact.businessContinuity ? 1 : 0;
            count++;
        }

        return count > 0 ? score / count : 0.5;
    }

    evaluateConfidence(actualConfidence, expectedRange) {
        if (!expectedRange) return 0.5;
        
        if (actualConfidence >= expectedRange.min && actualConfidence <= expectedRange.max) {
            return 1.0;
        }
        
        const distance = Math.min(
            Math.abs(actualConfidence - expectedRange.min),
            Math.abs(actualConfidence - expectedRange.max)
        );
        
        return Math.max(0, 1 - distance * 2);
    }

    // 生成测试报告
    generateReport() {
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(r => r.passed).length;
        const avgScore = this.testResults.reduce((sum, r) => sum + r.score, 0) / totalTests;

        console.log('\n=== 响应耗时风险检测算法测试报告 ===');
        console.log(`总测试数: ${totalTests}`);
        console.log(`通过测试: ${passedTests} (${(passedTests/totalTests*100).toFixed(1)}%)`);
        console.log(`平均得分: ${(avgScore*100).toFixed(1)}%`);
        console.log('\n详细结果:');

        this.testResults.forEach(result => {
            const status = result.passed ? '✅ PASS' : '❌ FAIL';
            console.log(`${status} ${result.testName}: ${(result.score*100).toFixed(1)}%`);
            
            if (!result.passed) {
                console.log(`  实际风险: ${(result.result.risk*100).toFixed(1)}%`);
                console.log(`  期望范围: ${(result.expectedOutcome.riskRange.min*100).toFixed(1)}%-${(result.expectedOutcome.riskRange.max*100).toFixed(1)}%`);
                console.log(`  告警数量: ${result.result.alerts.length} (期望: ${result.expectedOutcome.expectedAlerts?.length || 0})`);
            }
        });

        return {
            totalTests,
            passedTests,
            avgScore,
            details: this.testResults
        };
    }
}

// 执行测试套件
function runLatencyRiskTests() {
    const generator = new LatencyTestDataGenerator();
    const scorer = new TestScorer();

    // 基础耗时配置（微服务场景）
    const baseLatencies = {
        '1': 0.02,      // 平均20ms
        '0.5': 0.015,   // P50: 15ms
        '0.75': 0.025,  // P75: 25ms
        '0.95': 0.05,   // P95: 50ms
        '0.99': 0.1     // P99: 100ms
    };

    const times = generator.generateTimePoints(30); // 30分钟数据

    console.log('开始执行响应耗时风险检测算法测试...\n');

    // 测试1: 正常稳定场景
    console.log('测试1: 正常稳定场景');
    const normalCurrent = generator.generateNormalLatency(times, baseLatencies, 0.1);
    const normalYoy = generator.generateNormalLatency(times, baseLatencies, 0.1);

    const normalResult = detectLatencyRiskFromWindows(normalCurrent, normalYoy, {
        serviceType: 'MICROSERVICE'
    });

    const normalScore = scorer.scoreTest('正常稳定场景', normalResult, {
        riskRange: { min: 0.0, max: 0.2 },
        expectedAlerts: [],
        expectedBusinessImpact: {
            userExperience: 'MINIMAL',
            systemStability: 'STABLE',
            businessContinuity: 'NORMAL'
        },
        expectedConfidenceRange: { min: 0.6, max: 1.0 }
    });

    console.log(`结果: 风险=${(normalResult.risk*100).toFixed(1)}%, 置信度=${(normalResult.confidence*100).toFixed(0)}%, 告警=${normalResult.alerts.length}个`);
    console.log(`得分: ${(normalScore*100).toFixed(1)}%\n`);

    // 测试2: P99突发尖刺场景
    console.log('测试2: P99突发尖刺场景');
    const spikeCurrent = generator.generateSpikeScenario(times, baseLatencies, 20, 25, 8);
    const spikeYoy = generator.generateNormalLatency(times, baseLatencies, 0.1);

    const spikeResult = detectLatencyRiskFromWindows(spikeCurrent, spikeYoy, {
        serviceType: 'MICROSERVICE'
    });

    const spikeScore = scorer.scoreTest('P99突发尖刺场景', spikeResult, {
        riskRange: { min: 0.6, max: 1.0 },
        expectedAlerts: [
            { type: 'ABSOLUTE_LATENCY_CRITICAL', level: 'CRITICAL' },
            { type: 'LATENCY_DEGRADATION', level: 'WARNING' }
        ],
        expectedBusinessImpact: {
            userExperience: 'SIGNIFICANT',
            systemStability: 'CONCERNING',
            businessContinuity: 'MODERATE_RISK'
        },
        expectedConfidenceRange: { min: 0.5, max: 0.9 }
    });

    console.log(`结果: 风险=${(spikeResult.risk*100).toFixed(1)}%, 置信度=${(spikeResult.confidence*100).toFixed(0)}%, 告警=${spikeResult.alerts.length}个`);
    console.log(`得分: ${(spikeScore*100).toFixed(1)}%\n`);

    // 测试3: 系统性劣化场景
    console.log('测试3: 系统性劣化场景');
    const degradationCurrent = generator.generateSystemicDegradation(times, baseLatencies, 3);
    const degradationYoy = generator.generateNormalLatency(times, baseLatencies, 0.1);

    const degradationResult = detectLatencyRiskFromWindows(degradationCurrent, degradationYoy, {
        serviceType: 'MICROSERVICE'
    });

    const degradationScore = scorer.scoreTest('系统性劣化场景', degradationResult, {
        riskRange: { min: 0.7, max: 1.0 },
        expectedAlerts: [
            { type: 'ABSOLUTE_LATENCY_CRITICAL', level: 'CRITICAL' },
            { type: 'LATENCY_DEGRADATION', level: 'WARNING' },
            { type: 'HIGH_OVERALL_RISK', level: 'CRITICAL' }
        ],
        expectedBusinessImpact: {
            userExperience: 'SEVERE',
            systemStability: 'UNSTABLE',
            businessContinuity: 'CRITICAL'
        },
        expectedConfidenceRange: { min: 0.6, max: 0.9 }
    });

    console.log(`结果: 风险=${(degradationResult.risk*100).toFixed(1)}%, 置信度=${(degradationResult.confidence*100).toFixed(0)}%, 告警=${degradationResult.alerts.length}个`);
    console.log(`得分: ${(degradationScore*100).toFixed(1)}%\n`);

    // 测试4: 长尾恶化场景
    console.log('测试4: 长尾恶化场景');
    const tailCurrent = generator.generateTailDegradation(times, baseLatencies, 4);
    const tailYoy = generator.generateNormalLatency(times, baseLatencies, 0.1);

    const tailResult = detectLatencyRiskFromWindows(tailCurrent, tailYoy, {
        serviceType: 'MICROSERVICE'
    });

    const tailScore = scorer.scoreTest('长尾恶化场景', tailResult, {
        riskRange: { min: 0.4, max: 0.8 },
        expectedAlerts: [
            { type: 'TAIL_LATENCY_DEGRADATION', level: 'WARNING' },
            { type: 'LATENCY_DEGRADATION', level: 'WARNING' }
        ],
        expectedBusinessImpact: {
            userExperience: 'MODERATE',
            systemStability: 'STABLE',
            businessContinuity: 'MODERATE_RISK'
        },
        expectedConfidenceRange: { min: 0.5, max: 0.8 }
    });

    console.log(`结果: 风险=${(tailResult.risk*100).toFixed(1)}%, 置信度=${(tailResult.confidence*100).toFixed(0)}%, 告警=${tailResult.alerts.length}个`);
    console.log(`得分: ${(tailScore*100).toFixed(1)}%\n`);

    // 测试5: 高峰时段模式（同比也有高峰）
    console.log('测试5: 高峰时段模式场景');
    const peakCurrent = generator.generatePeakPattern(times, baseLatencies, 2.0);
    const peakYoy = generator.generatePeakPattern(times, baseLatencies, 1.8); // 同比也有高峰特征

    const peakResult = detectLatencyRiskFromWindows(peakCurrent, peakYoy, {
        serviceType: 'MICROSERVICE'
    });

    const peakScore = scorer.scoreTest('高峰时段模式场景', peakResult, {
        riskRange: { min: 0.02, max: 0.15 }, // 调整为更合理的期望范围
        expectedAlerts: [], // 不应该有严重告警
        expectedBusinessImpact: {
            userExperience: 'MINIMAL',
            systemStability: 'STABLE',
            businessContinuity: 'NORMAL'
        },
        expectedConfidenceRange: { min: 0.4, max: 0.8 }
    });

    console.log(`结果: 风险=${(peakResult.risk*100).toFixed(1)}%, 置信度=${(peakResult.confidence*100).toFixed(0)}%, 告警=${peakResult.alerts.length}个`);
    console.log(`得分: ${(peakScore*100).toFixed(1)}%\n`);

    // 测试6: 性能改善场景
    console.log('测试6: 性能改善场景');
    const improvementCurrent = generator.generatePerformanceImprovement(times, baseLatencies, 0.6);
    const improvementYoy = generator.generateNormalLatency(times, baseLatencies, 0.1);

    const improvementResult = detectLatencyRiskFromWindows(improvementCurrent, improvementYoy, {
        serviceType: 'MICROSERVICE'
    });

    const improvementScore = scorer.scoreTest('性能改善场景', improvementResult, {
        riskRange: { min: 0.0, max: 0.1 },
        expectedAlerts: [
            { type: 'PERFORMANCE_IMPROVEMENT', level: 'INFO' }
        ],
        expectedBusinessImpact: {
            userExperience: 'MINIMAL',
            systemStability: 'STABLE',
            businessContinuity: 'NORMAL'
        },
        expectedConfidenceRange: { min: 0.7, max: 1.0 }
    });

    console.log(`结果: 风险=${(improvementResult.risk*100).toFixed(1)}%, 置信度=${(improvementResult.confidence*100).toFixed(0)}%, 告警=${improvementResult.alerts.length}个`);
    console.log(`得分: ${(improvementScore*100).toFixed(1)}%\n`);

    // 测试7: 外网依赖场景
    console.log('测试7: 外网依赖场景');
    const externalDepCurrent = generator.generateExternalDependencyScenario(times, baseLatencies, 5, 3);
    const externalDepYoy = generator.generateExternalDependencyScenario(times, baseLatencies, 4.5, 2.8); // 同比也有类似模式

    const externalDepResult = detectLatencyRiskFromWindows(externalDepCurrent, externalDepYoy, {
        serviceType: 'MICROSERVICE'
    });

    const externalDepScore = scorer.scoreTest('外网依赖场景', externalDepResult, {
        riskRange: { min: 0.2, max: 0.6 }, // 应该有风险但被合理折扣
        expectedAlerts: [
            { type: 'EXTERNAL_DEPENDENCY_IMPACT', level: 'INFO' }
        ],
        expectedBusinessImpact: {
            userExperience: 'MINOR', // 外网依赖场景下用户体验影响有限
            systemStability: 'WATCHFUL',
            businessContinuity: 'NORMAL'
        },
        expectedConfidenceRange: { min: 0.5, max: 0.9 }
    });

    console.log(`结果: 风险=${(externalDepResult.risk*100).toFixed(1)}%, 置信度=${(externalDepResult.confidence*100).toFixed(0)}%, 告警=${externalDepResult.alerts.length}个`);

    // 显示外网依赖检测详情
    if (externalDepResult.externalDependencyPattern) {
        const pattern = externalDepResult.externalDependencyPattern;
        console.log(`外网依赖检测: ${pattern.isExternalDependency ? '✅检测到' : '❌未检测到'} (置信度: ${(pattern.confidence*100).toFixed(1)}%)`);
        console.log(`P99/P50比值: ${pattern.p99_p50_ratio.toFixed(1)}, P95/P50比值: ${pattern.p95_p50_ratio.toFixed(1)}`);
        console.log(`证据: ${pattern.evidence.join(', ')}`);
    }

    console.log(`得分: ${(externalDepScore*100).toFixed(1)}%\n`);

    // 生成测试报告
    const report = scorer.generateReport();

    // 算法优化建议
    console.log('\n=== 算法优化建议 ===');
    if (report.avgScore < 0.8) {
        console.log('⚠️  算法需要优化，建议调整以下参数：');

        const failedTests = report.details.filter(t => !t.passed);
        failedTests.forEach(test => {
            console.log(`- ${test.testName}: 考虑调整相关阈值参数`);
        });
    } else {
        console.log('✅ 算法表现良好，达到预期效果');
    }

    return report;
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
    runLatencyRiskTests();
}

// 导出测试工具和测试函数
module.exports = {
    LatencyTestDataGenerator,
    TestScorer,
    runLatencyRiskTests
};
