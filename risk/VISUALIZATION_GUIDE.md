# 响应耗时风险检测算法可视化指南

本指南介绍如何使用响应耗时风险检测算法的可视化测试页面。

## 📁 文件说明

### 核心文件
- **`latency-risk-detector.js`** - 响应耗时风险检测算法核心实现
- **`latency-test.html`** - 完整功能的可视化测试页面
- **`latency-demo.html`** - 简化的演示页面
- **`latency-risk-detector.test.js`** - 算法测试套件

### 可视化页面对比

| 特性 | latency-test.html | latency-demo.html |
|------|-------------------|-------------------|
| **功能完整性** | 完整功能 | 核心功能 |
| **数据输入** | 手动输入JSON | 预设场景 |
| **图表可视化** | Chart.js多视图图表 | 简化视图切换 |
| **配置选项** | 高级配置面板 | 固定配置 |
| **多视图支持** | 4种视图模式 | 3种视图模式 |
| **适用场景** | 开发测试 | 快速演示 |

## 🚀 快速开始

### 方法1：本地HTTP服务器
```bash
# 在risk目录下启动HTTP服务器
cd risk
python3 -m http.server 8080

# 或使用Node.js
npx http-server -p 8080

# 访问页面
# 演示页面: http://localhost:8080/latency-demo.html
# 完整页面: http://localhost:8080/latency-test.html
```

### 方法2：直接打开文件
```bash
# 直接在浏览器中打开HTML文件
open latency-demo.html
# 或
open latency-test.html
```

## 🎯 演示页面使用指南 (latency-demo.html)

### 主要功能
1. **预设测试场景** - 6种典型的耗时异常场景
2. **一键测试** - 点击按钮即可运行分析
3. **结果展示** - 风险评估、分位数详情、智能告警

### 测试场景说明

#### 1. 正常稳定场景 🟢
- **特征**: 各分位数稳定运行，无异常波动
- **期望结果**: 低风险(0-20%)，无告警
- **业务含义**: 系统运行正常

#### 2. P99突发尖刺场景 🟡
- **特征**: P99在短时间内大幅上升(8倍)
- **期望结果**: 高风险(60-100%)，CRITICAL告警
- **业务含义**: 个别请求出现严重延迟

#### 3. 系统性劣化场景 🔴
- **特征**: 所有分位数同步上升(3倍)
- **期望结果**: 高风险(70-100%)，多个告警
- **业务含义**: 系统整体性能下降

#### 4. 长尾恶化场景 🟡
- **特征**: P99上升但平均值稳定
- **期望结果**: 中风险(40-80%)，长尾告警
- **业务含义**: 部分用户体验受影响

#### 5. 高峰时段模式 🔵
- **特征**: 业务高峰期的正常模式
- **期望结果**: 低风险(2-15%)，无告警
- **业务含义**: 正常业务高峰，算法自动适应

#### 6. 性能改善场景 🟢
- **特征**: 各分位数下降，性能优化
- **期望结果**: 低风险(0-10%)，改善通知
- **业务含义**: 系统性能优化生效

#### 7. 外网依赖场景 🟡
- **特征**: P99/P95上升但P50/P75稳定
- **期望结果**: 中低风险(20-40%)，外网依赖告警
- **业务含义**: 外网服务影响高分位数，但整体可控

### 结果解读

#### 风险等级
- **低风险** (0-40%): 🟢 系统运行正常
- **中风险** (40-70%): 🟡 建议关注监控
- **高风险** (70-100%): 🔴 需要立即处理

#### 分位数说明
- **AVG**: 平均响应时间
- **P50**: 50%请求的响应时间
- **P75**: 75%请求的响应时间
- **P95**: 95%请求的响应时间(SLA关键指标)
- **P99**: 99%请求的响应时间(用户体验关键)

#### 告警级别
- **CRITICAL**: 🔴 严重告警，需立即处理
- **WARNING**: 🟡 警告告警，建议关注
- **INFO**: 🔵 信息通知，记录备案

## 🔧 完整测试页面使用指南 (latency-test.html)

### 高级功能
1. **自定义数据输入** - 支持JSON格式的耗时数据
2. **实时图表可视化** - Chart.js多分位数趋势图
3. **高级配置选项** - 算法参数调优
4. **详细结果分析** - 完整的风险分析报告

### 数据格式要求

#### 输入数据格式
```json
[
  {"time": 1757179920, "value": "0.025", "category": "1"},      // 平均值
  {"time": 1757179920, "value": "0.018", "category": "0.5"},   // P50
  {"time": 1757179920, "value": "0.030", "category": "0.75"},  // P75
  {"time": 1757179920, "value": "0.055", "category": "0.95"},  // P95
  {"time": 1757179920, "value": "0.120", "category": "0.99"},  // P99
  // ... 更多时间点
]
```

#### 字段说明
- **time**: Unix时间戳(秒)
- **value**: 响应时间(秒，字符串格式)
- **category**: 分位数标识
  - `"1"`: 平均值
  - `"0.5"`: P50中位数
  - `"0.75"`: P75
  - `"0.95"`: P95
  - `"0.99"`: P99

## 🎨 多视图功能详解

### 视图模式说明

#### 1. 仅当前模式
- **用途**: 专注分析当前时段的耗时趋势
- **适用场景**: 初步了解系统当前状态
- **显示内容**: 5条实线（P99/P95/P75/P50/AVG）
- **优势**: 界面简洁，趋势清晰

#### 2. 仅同比模式
- **用途**: 查看历史同期的耗时基线
- **适用场景**: 了解正常业务模式
- **显示内容**: 5条虚线（同比数据）
- **优势**: 建立性能基线认知

#### 3. 对比视图模式 ⭐ 推荐
- **用途**: 直观对比当前与同比的差异
- **适用场景**: 异常分析和根因定位
- **显示内容**: 10条线（5条实线+5条虚线）
- **颜色规则**: 相同分位数使用相同颜色，线型区分时期
- **优势**: 最直观的对比分析

#### 4. 差值视图模式
- **用途**: 量化分析性能变化幅度
- **适用场景**: 精确评估优化效果或劣化程度
- **显示内容**: 5条线显示变化百分比
- **Y轴含义**: 正值=劣化，负值=改善
- **优势**: 数值化的变化分析

### 分位数控制功能

#### 选择性显示
- **P99**: 🔴 用户体验关键指标，权重最高
- **P95**: 🟡 SLA关键指标，平衡性能
- **P75**: 🔵 整体性能指标，反映大部分请求
- **P50**: 🟢 中位数指标，系统基础性能
- **AVG**: 🟣 平均值指标，整体趋势参考

#### 使用技巧
1. **初步分析**: 只显示P99和P95，关注长尾性能
2. **全面分析**: 显示所有分位数，了解整体分布
3. **对比分析**: 在对比模式下，重点关注差异最大的分位数
4. **趋势分析**: 在差值模式下，观察各分位数的变化趋势

### 配置参数说明

#### 服务类型
- **微服务**: 通用微服务API
- **数据库**: 数据库查询操作
- **API网关**: API网关服务
- **Web API**: Web应用API
- **批处理**: 批处理任务

#### 算法参数
- **Z阈值** (1.0-3.0): 异常检测敏感度，越小越敏感
- **劣化阈值** (0.1-0.5): 性能劣化判定阈值
- **模式学习**: 是否启用同比模式学习

## 📊 可视化特性

### 图表功能 (latency-test.html)
- **多分位数趋势图**: 显示所有分位数的时间趋势
- **多视图模式**: 支持4种数据展示模式
  - **仅当前**: 只显示当前时段数据
  - **仅同比**: 只显示同比时段数据
  - **对比视图**: 同时显示当前和同比数据（实线vs虚线）
  - **差值视图**: 显示相对同比的变化百分比
- **交互式控制**: 可单独显示/隐藏任意分位数
- **智能图例**: 自动区分当前数据（实线）和同比数据（虚线）
- **异常标记**: 在检测到异常的时间点添加视觉标记
- **实时更新**: 分析结果和视图切换实时更新图表

### 响应式设计
- **桌面端**: 三栏布局，功能完整
- **平板端**: 两栏布局，自适应调整
- **手机端**: 单栏布局，垂直排列

## 🛠️ 开发和调试

### 浏览器兼容性
- **Chrome**: 完全支持
- **Firefox**: 完全支持
- **Safari**: 完全支持
- **Edge**: 完全支持

### 调试技巧
1. **打开开发者工具**: F12或右键检查
2. **查看控制台**: 检查JavaScript错误
3. **网络面板**: 检查资源加载情况
4. **算法调试**: 在控制台中调用`detectLatencyRiskFromWindows`

### 常见问题

#### Q: 页面显示空白或加载失败
A: 检查以下几点：
- 确保所有文件在同一目录下
- 使用HTTP服务器而非直接打开文件
- 检查浏览器控制台的错误信息

#### Q: 算法分析失败
A: 检查数据格式：
- JSON格式是否正确
- 时间戳是否为数字
- 分位数标识是否正确
- 数据点数量是否足够(建议≥10个时间点)

#### Q: 图表不显示
A: 确认：
- Chart.js库是否正确加载
- 网络连接是否正常
- 浏览器是否支持Canvas

## 🎨 自定义和扩展

### 添加新的测试场景
```javascript
// 在latency-demo.html中添加新场景
const scenarios = {
    // ... 现有场景
    custom: {
        name: "自定义场景",
        description: "场景描述",
        current: generateCustomData(),
        yoy: generateNormalData()
    }
};
```

### 修改样式主题
```css
/* 修改主色调 */
:root {
    --primary: #your-color;
    --accent: #your-accent;
}
```

### 集成到现有系统
```javascript
// 调用算法API
const result = detectLatencyRiskFromWindows(currentData, yoyData, config);

// 处理结果
if (result.risk > 0.7) {
    // 触发告警
    triggerAlert(result.alerts);
}
```

## 📈 最佳实践

### 数据准备
1. **时间窗口**: 建议使用30-60分钟的数据窗口
2. **数据频率**: 1分钟间隔的数据点效果最佳
3. **同比数据**: 使用上周同时段数据作为基线

### 参数调优
1. **敏感度调节**: 根据业务特点调整Z阈值
2. **服务类型**: 选择正确的服务类型获得最佳效果
3. **模式学习**: 对于有明显高峰低谷的业务建议启用

### 告警处理
1. **分级响应**: 根据告警级别制定不同的响应策略
2. **告警聚合**: 避免告警风暴，合理聚合相关告警
3. **根因分析**: 结合业务上下文分析告警根因

---

## 🔗 相关链接

- [算法详细文档](./README.md)
- [算法对比分析](./ALGORITHM_COMPARISON.md)
- [测试套件说明](./latency-risk-detector.test.js)
- [调试工具](./debug-latency-test.js)
