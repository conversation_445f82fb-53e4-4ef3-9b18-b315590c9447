#!/bin/bash

# 多视图功能演示脚本

echo "🎨 响应耗时风险检测 - 多视图功能演示"
echo "================================================"

# 检查环境
if [ ! -f "latency-risk-detector.js" ]; then
    echo "❌ 错误: 请在risk目录下运行此脚本"
    exit 1
fi

echo "✅ 环境检查通过"
echo ""

# 功能测试
echo "🧪 运行多视图功能测试..."
if command -v node &> /dev/null; then
    node test-visualization.js
    echo ""
else
    echo "⚠️  Node.js未安装，跳过功能测试"
    echo ""
fi

# 启动演示服务器
echo "🚀 启动多视图演示服务器..."
echo ""
echo "📱 可用页面:"
echo "   1. 简化演示页面: http://localhost:8080/latency-demo.html"
echo "   2. 完整功能页面: http://localhost:8080/latency-test.html"
echo "   3. 多视图测试页面: http://localhost:8080/test-multiview.html"
echo "   4. URL传参测试页面: http://localhost:8080/test-url-params.html"
echo ""
echo "🎯 多视图功能特点:"
echo "   • 4种视图模式: 仅当前/仅同比/对比视图/差值视图"
echo "   • 5个分位数: P99/P95/P75/P50/AVG"
echo "   • 交互式控制: 可单独显示/隐藏任意分位数"
echo "   • 智能图例: 实线(当前) vs 虚线(同比)"
echo "   • 差值分析: 量化性能变化百分比"
echo "   • URL传参支持: 场景参数 + 压缩数据传参"
echo "   • 数据分享: 一键生成分享链接"
echo ""
echo "💡 使用建议:"
echo "   1. 先用'仅当前'模式了解整体趋势"
echo "   2. 切换到'对比视图'发现异常差异"
echo "   3. 使用'差值视图'量化变化幅度"
echo "   4. 通过分位数控制聚焦关键指标"
echo ""
echo "按 Ctrl+C 停止服务器"
echo "================================================"

# 启动HTTP服务器
if command -v python3 &> /dev/null; then
    python3 -m http.server 8080
elif command -v python &> /dev/null; then
    python -m SimpleHTTPServer 8080
elif command -v npx &> /dev/null; then
    npx http-server -p 8080
else
    echo "❌ 错误: 未找到Python或Node.js"
    echo "请安装Python3或Node.js后重试"
    echo ""
    echo "或者直接在浏览器中打开HTML文件:"
    echo "📱 简化演示: $(pwd)/latency-demo.html"
    echo "🔧 完整功能: $(pwd)/latency-test.html"
    echo "🧪 功能测试: $(pwd)/test-multiview.html"
    exit 1
fi
