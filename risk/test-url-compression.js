/**
 * URL传参和数据压缩功能测试
 */

// 压缩算法实现
function ultraCompressLatencyData(data) {
    if (!data || data.length === 0) return '';
    
    try {
        const baseTime = data[0].time;
        const interval = 60;
        
        const timeGroups = {};
        data.forEach(item => {
            const timeIndex = Math.round((item.time - baseTime) / interval);
            if (!timeGroups[timeIndex]) timeGroups[timeIndex] = {};
            timeGroups[timeIndex][item.category] = parseFloat(item.value);
        });
        
        let compressed = '';
        compressed += baseTime.toString(36) + '|';
        compressed += interval.toString(36) + '|';
        
        const categories = ['1', '0.5', '0.75', '0.95', '0.99'];
        const timeIndices = Object.keys(timeGroups).map(Number).sort((a, b) => a - b);
        
        const dataGroups = timeIndices.map(timeIndex => {
            const group = timeGroups[timeIndex];
            const values = categories.map(cat => {
                const val = group[cat] || 0;
                const quantized = Math.min(65535, Math.max(0, Math.round(val * 6553.5)));
                return quantized;
            });
            return values.map(v => v.toString(36)).join('.');
        });
        
        compressed += dataGroups.join(',');
        return compressed;
        
    } catch (error) {
        console.error('压缩失败:', error);
        return '';
    }
}

function ultraDecompressLatencyData(compressedStr) {
    if (!compressedStr) return [];
    
    try {
        const parts = compressedStr.split('|');
        if (parts.length < 3) return [];
        
        const baseTime = parseInt(parts[0], 36);
        const interval = parseInt(parts[1], 36);
        const dataStr = parts[2];
        
        const dataGroups = dataStr.split(',');
        const categories = ['1', '0.5', '0.75', '0.95', '0.99'];
        const result = [];
        
        dataGroups.forEach((group, timeIndex) => {
            if (!group) return;
            
            const values = group.split('.');
            if (values.length !== 5) return;
            
            const time = baseTime + timeIndex * interval;
            
            values.forEach((valueStr, catIndex) => {
                const quantizedValue = parseInt(valueStr, 36);
                if (isNaN(quantizedValue)) return;
                
                const originalValue = quantizedValue / 6553.5;
                
                result.push({
                    time: time,
                    value: originalValue.toFixed(6),
                    category: categories[catIndex]
                });
            });
        });
        
        return result;
        
    } catch (error) {
        console.error('解压缩失败:', error);
        return [];
    }
}

// 生成测试数据
function generateTestData(scenario = 'spike', points = 20) {
    const baseTime = Math.floor(Date.now() / 1000);
    const baseLatencies = {
        '1': 0.02,      // 平均20ms
        '0.5': 0.015,   // P50: 15ms
        '0.75': 0.025,  // P75: 25ms
        '0.95': 0.05,   // P95: 50ms
        '0.99': 0.1     // P99: 100ms
    };

    const data = [];
    for (let i = 0; i < points; i++) {
        const time = baseTime + i * 60;
        
        Object.entries(baseLatencies).forEach(([category, baseValue]) => {
            let value = baseValue;
            
            // 根据场景调整数值
            switch (scenario) {
                case 'spike':
                    if (i >= 10 && i <= 15) {
                        const multiplier = category === '0.99' ? 8 :
                                         category === '0.95' ? 5 :
                                         category === '0.75' ? 2 : 1.2;
                        value = baseValue * multiplier;
                    }
                    break;
                case 'degradation':
                    const factor = 1 + 2 * (i / points);
                    value = baseValue * factor;
                    break;
                case 'improvement':
                    const improveFactor = 1 - 0.4 * (i / points);
                    value = baseValue * improveFactor;
                    break;
            }
            
            data.push({
                time: time,
                value: value.toFixed(6),
                category: category
            });
        });
    }
    return data;
}

// 测试压缩功能
function testCompression() {
    console.log('🗜️ 测试数据压缩功能');
    console.log('========================');
    
    const scenarios = ['normal', 'spike', 'degradation', 'improvement'];
    const pointCounts = [10, 20, 30, 50];
    
    scenarios.forEach(scenario => {
        console.log(`\n📊 ${scenario.toUpperCase()} 场景:`);
        
        pointCounts.forEach(points => {
            const testData = generateTestData(scenario, points);
            const originalJson = JSON.stringify(testData);
            const compressed = ultraCompressLatencyData(testData);
            const decompressed = ultraDecompressLatencyData(compressed);
            
            const compressionRatio = ((1 - compressed.length / originalJson.length) * 100).toFixed(1);
            const isValid = decompressed.length === testData.length;
            
            console.log(`  ${points}点: ${originalJson.length}→${compressed.length}字符 (${compressionRatio}% 压缩) ${isValid ? '✅' : '❌'}`);
        });
    });
}

// 测试URL生成
function testUrlGeneration() {
    console.log('\n🔗 测试URL生成功能');
    console.log('========================');
    
    const scenarios = ['spike', 'degradation', 'improvement'];
    
    scenarios.forEach(scenario => {
        const testData = generateTestData(scenario, 20);
        const compressed = ultraCompressLatencyData(testData);
        
        if (compressed) {
            // 生成不同类型的URL
            const urls = {
                demo: `latency-demo.html?data=${encodeURIComponent(compressed)}&view=comparison`,
                full: `latency-test.html?data=${encodeURIComponent(compressed)}&view=comparison&service=MICROSERVICE`,
                scenario: `latency-demo.html?scenario=${scenario}&view=comparison`
            };
            
            console.log(`\n📱 ${scenario.toUpperCase()} 场景:`);
            console.log(`  压缩数据长度: ${compressed.length} 字符`);
            console.log(`  演示页面URL: ${urls.demo.length} 字符`);
            console.log(`  完整页面URL: ${urls.full.length} 字符`);
            console.log(`  场景参数URL: ${urls.scenario.length} 字符`);
            console.log(`  URL适用性: ${urls.full.length < 2000 ? '✅ 适合' : '⚠️ 过长'}`);
        }
    });
}

// 测试数据完整性
function testDataIntegrity() {
    console.log('\n🔍 测试数据完整性');
    console.log('========================');
    
    const testData = generateTestData('spike', 30);
    const compressed = ultraCompressLatencyData(testData);
    const decompressed = ultraDecompressLatencyData(compressed);
    
    console.log(`原始数据点: ${testData.length}`);
    console.log(`解压数据点: ${decompressed.length}`);
    
    if (testData.length === decompressed.length) {
        let maxError = 0;
        let errorCount = 0;
        
        for (let i = 0; i < testData.length; i++) {
            const original = parseFloat(testData[i].value);
            const restored = parseFloat(decompressed[i].value);
            const error = Math.abs(original - restored);
            
            maxError = Math.max(maxError, error);
            if (error > 0.001) errorCount++; // 超过1ms误差
            
            if (testData[i].time !== decompressed[i].time || 
                testData[i].category !== decompressed[i].category) {
                console.log(`❌ 元数据不匹配: 索引${i}`);
                return;
            }
        }
        
        console.log(`✅ 数据完整性验证通过`);
        console.log(`   最大误差: ${(maxError * 1000).toFixed(3)}ms`);
        console.log(`   超差点数: ${errorCount}/${testData.length}`);
        console.log(`   精度评估: ${maxError < 0.001 ? '优秀' : maxError < 0.01 ? '良好' : '一般'}`);
    } else {
        console.log(`❌ 数据点数量不匹配`);
    }
}

// 生成示例URL
function generateExampleUrls() {
    console.log('\n🌐 生成示例URL');
    console.log('========================');
    
    const baseUrl = 'http://localhost:8080/';
    const examples = [
        {
            name: '基础场景参数',
            urls: [
                'latency-demo.html?scenario=spike',
                'latency-demo.html?scenario=degradation&view=difference',
                'latency-test.html?scenario=spike&service=DATABASE&view=comparison'
            ]
        },
        {
            name: '压缩数据参数',
            urls: []
        }
    ];
    
    // 生成压缩数据URL示例
    const spikeData = generateTestData('spike', 15);
    const compressed = ultraCompressLatencyData(spikeData);
    
    if (compressed) {
        examples[1].urls = [
            `latency-demo.html?data=${encodeURIComponent(compressed)}`,
            `latency-demo.html?data=${encodeURIComponent(compressed)}&view=comparison`,
            `latency-test.html?data=${encodeURIComponent(compressed)}&view=difference&service=MICROSERVICE`
        ];
    }
    
    examples.forEach(example => {
        console.log(`\n📋 ${example.name}:`);
        example.urls.forEach((url, index) => {
            console.log(`  ${index + 1}. ${baseUrl}${url}`);
            console.log(`     长度: ${url.length} 字符`);
        });
    });
}

// 主测试函数
function runUrlCompressionTests() {
    console.log('🧪 URL传参和数据压缩功能测试');
    console.log('==========================================');
    
    testCompression();
    testUrlGeneration();
    testDataIntegrity();
    generateExampleUrls();
    
    console.log('\n==========================================');
    console.log('🎉 URL传参功能测试完成！');
    console.log('\n💡 使用建议:');
    console.log('   • 小数据集(<20点): 使用压缩数据传参');
    console.log('   • 大数据集(>30点): 使用场景参数');
    console.log('   • 分享链接: 优先使用场景参数，简洁易读');
    console.log('   • 自定义数据: 使用压缩传参，支持完整自定义');
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
    runUrlCompressionTests();
}

module.exports = {
    ultraCompressLatencyData,
    ultraDecompressLatencyData,
    generateTestData,
    runUrlCompressionTests
};
