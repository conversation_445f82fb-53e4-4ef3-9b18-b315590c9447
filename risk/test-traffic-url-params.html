<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流量检测URL传参测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .url-example {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            word-break: break-all;
            border-left: 4px solid #007bff;
        }
        .test-link {
            display: inline-block;
            padding: 8px 16px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin: 5px;
            font-size: 0.9rem;
        }
        .test-link:hover {
            background: #0056b3;
        }
        .compression-info {
            background: #e7f3ff;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #17a2b8;
        }
        .success { color: #28a745; }
        .warning { color: #ffc107; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 流量检测URL传参测试</h1>
        <p>测试流量异常检测算法的URL传参和数据压缩功能</p>

        <div class="test-section">
            <h3>📊 压缩算法测试</h3>
            <div>
                <button onclick="testCompression()">运行压缩测试</button>
                <div id="compressionResult"></div>
            </div>
        </div>

        <div class="test-section">
            <h3>🎯 场景参数测试</h3>
            <p>使用预设场景参数生成测试链接：</p>
            
            <div>
                <h4>简单格式场景链接</h4>
                <a href="index.html?scenario=normal&format=simple" class="test-link" target="_blank">正常流量</a>
                <a href="index.html?scenario=spike&format=simple" class="test-link" target="_blank">流量突增</a>
                <a href="index.html?scenario=drop&format=simple" class="test-link" target="_blank">流量骤降</a>
                <a href="index.html?scenario=oscillation&format=simple" class="test-link" target="_blank">流量震荡</a>
            </div>

            <div>
                <h4>JSON格式场景链接</h4>
                <a href="index.html?scenario=normal&format=json" class="test-link" target="_blank">正常流量(JSON)</a>
                <a href="index.html?scenario=spike&format=json" class="test-link" target="_blank">流量突增(JSON)</a>
                <a href="index.html?scenario=drop&format=json" class="test-link" target="_blank">流量骤降(JSON)</a>
            </div>
        </div>

        <div class="test-section">
            <h3>🗜️ 压缩数据传参测试</h3>
            <div>
                <button onclick="generateCompressedUrls()">生成压缩数据链接</button>
                <div id="compressedUrls"></div>
            </div>
        </div>

        <div class="test-section">
            <h3>📋 测试说明</h3>
            <div class="compression-info">
                <h4>支持的URL参数：</h4>
                <ul>
                    <li><strong>scenario</strong>: 预设场景 (normal, spike, drop, oscillation)</li>
                    <li><strong>format</strong>: 数据格式 (simple, json)</li>
                    <li><strong>data</strong>: 压缩的当前数据</li>
                    <li><strong>yoy</strong>: 压缩的同比数据 (可选，默认与data相同)</li>
                    <li><strong>threshold</strong>: 异常阈值 (默认2.0)</li>
                    <li><strong>min_samples</strong>: 最小样本数 (默认5)</li>
                    <li><strong>trend</strong>: 趋势分析 (true/false，默认true)</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // 压缩算法实现
        function compressSimpleArray(data) {
            let compressed = '';
            compressed += data.length.toString(36) + '|';
            
            if (data.length > 0) {
                compressed += data[0].toString(36) + '|';
                
                const deltas = [];
                for (let i = 1; i < data.length; i++) {
                    deltas.push(data[i] - data[i-1]);
                }
                
                compressed += deltas.map(d => d.toString(36)).join(',');
            }
            
            return compressed;
        }

        function compressTimeValueArray(data) {
            if (data.length === 0) return '';
            
            const baseTime = data[0].time;
            const interval = data.length > 1 ? data[1].time - data[0].time : 60;
            
            let compressed = '';
            compressed += baseTime.toString(36) + '|';
            compressed += interval.toString(36) + '|';
            compressed += data.length.toString(36) + '|';
            
            const values = data.map(item => parseInt(item.value));
            const baseValue = values[0];
            compressed += baseValue.toString(36) + '|';
            
            const deltas = [];
            for (let i = 1; i < values.length; i++) {
                deltas.push(values[i] - values[i-1]);
            }
            
            compressed += deltas.map(d => d.toString(36)).join(',');
            
            return compressed;
        }

        function generateTestData(scenario = 'normal') {
            const baseValues = {
                normal: [100, 105, 98, 112, 120, 115, 108, 95, 102, 118, 125, 110, 95, 88, 92, 105, 115, 122],
                spike: [100, 105, 98, 112, 180, 220, 195, 115, 108, 95, 102, 118, 125, 110, 95, 88, 92, 105],
                drop: [100, 105, 98, 112, 120, 115, 45, 35, 40, 95, 102, 118, 125, 110, 95, 88, 92, 105],
                oscillation: [100, 150, 80, 160, 70, 170, 60, 180, 50, 190, 102, 118, 125, 110, 95, 88, 92, 105]
            };

            const simpleData = baseValues[scenario] || baseValues.normal;
            
            const baseTime = Math.floor(Date.now() / 1000);
            const jsonData = simpleData.map((value, index) => ({
                time: baseTime + index * 60,
                value: value.toString()
            }));
            
            return { simpleData, jsonData };
        }

        function testCompression() {
            const scenarios = ['normal', 'spike', 'drop', 'oscillation'];
            let html = '<h4>压缩测试结果：</h4>';
            
            scenarios.forEach(scenario => {
                const { simpleData, jsonData } = generateTestData(scenario);
                
                // 简单格式压缩
                const simpleOriginal = JSON.stringify(simpleData);
                const simpleCompressed = compressSimpleArray(simpleData);
                const simpleRatio = ((1 - simpleCompressed.length / simpleOriginal.length) * 100).toFixed(1);
                
                // JSON格式压缩
                const jsonOriginal = JSON.stringify(jsonData);
                const jsonCompressed = compressTimeValueArray(jsonData);
                const jsonRatio = ((1 - jsonCompressed.length / jsonOriginal.length) * 100).toFixed(1);
                
                html += `
                    <div class="compression-info">
                        <h5>${scenario.toUpperCase()} 场景</h5>
                        <p><strong>简单格式:</strong> ${simpleOriginal.length}→${simpleCompressed.length}字符 (${simpleRatio}% 压缩)</p>
                        <p><strong>JSON格式:</strong> ${jsonOriginal.length}→${jsonCompressed.length}字符 (${jsonRatio}% 压缩)</p>
                        <p><strong>压缩数据:</strong> <code>${simpleCompressed}</code></p>
                    </div>
                `;
            });
            
            document.getElementById('compressionResult').innerHTML = html;
        }

        function generateCompressedUrls() {
            const scenarios = ['spike', 'drop', 'oscillation'];
            let html = '<h4>生成的压缩数据链接：</h4>';
            
            scenarios.forEach(scenario => {
                const { simpleData, jsonData } = generateTestData(scenario);
                
                const simpleCompressed = compressSimpleArray(simpleData);
                const jsonCompressed = compressTimeValueArray(jsonData);
                
                if (simpleCompressed && jsonCompressed) {
                    const baseUrl = window.location.origin + window.location.pathname.replace('test-traffic-url-params.html', '');
                    
                    const simpleUrl = `${baseUrl}index.html?data=${encodeURIComponent(simpleCompressed)}&format=simple&threshold=2.0`;
                    const jsonUrl = `${baseUrl}index.html?data=${encodeURIComponent(jsonCompressed)}&format=json&threshold=2.0`;
                    
                    html += `
                        <div style="margin: 15px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                            <h5>${scenario.toUpperCase()} 场景</h5>
                            
                            <p><strong>简单格式链接:</strong></p>
                            <div class="url-example">${simpleUrl}</div>
                            <a href="${simpleUrl}" class="test-link" target="_blank">打开简单格式</a>
                            
                            <p><strong>JSON格式链接:</strong></p>
                            <div class="url-example">${jsonUrl}</div>
                            <a href="${jsonUrl}" class="test-link" target="_blank">打开JSON格式</a>
                            
                            <p><strong>链接长度:</strong> 简单格式 ${simpleUrl.length} 字符, JSON格式 ${jsonUrl.length} 字符</p>
                        </div>
                    `;
                }
            });
            
            document.getElementById('compressedUrls').innerHTML = html;
        }

        // 页面加载时运行基础测试
        window.addEventListener('load', () => {
            testCompression();
        });
    </script>
</body>
</html>
