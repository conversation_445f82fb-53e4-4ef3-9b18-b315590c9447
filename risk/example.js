/**
 * 流量监控风险检测算法 - 使用示例
 */

const { detectRiskFromWindows } = require('./test-risk-detector.js');

console.log('🚀 流量监控风险检测算法 - 使用示例');
console.log('=' .repeat(50));
console.log();

// 示例场景
const examples = [
    {
        name: '🟢 正常业务流量',
        current: [1050, 1020, 980, 1030, 990],
        yoy: [1000, 1000, 1000, 1000, 1000],
        description: '5%以内的正常波动'
    },
    {
        name: '🟡 需要关注',
        current: [1250, 1220, 1280, 1230, 1290],
        yoy: [1000, 1000, 1000, 1000, 1000],
        description: '25%流量增长，需要关注'
    },
    {
        name: '🟠 中等风险',
        current: [1500, 1520, 1480, 1530, 1490],
        yoy: [1000, 1000, 1000, 1000, 1000],
        description: '50%流量增长，中等风险'
    },
    {
        name: '🔴 高风险 - 流量翻倍',
        current: [2000, 2020, 1980, 2030, 1990],
        yoy: [1000, 1000, 1000, 1000, 1000],
        description: '100%流量增长，极高风险'
    },
    {
        name: '🔴 服务异常',
        current: [300, 320, 280, 330, 290],
        yoy: [1000, 1000, 1000, 1000, 1000],
        description: '流量大幅下降，可能服务异常'
    },
    {
        name: '🟢 小流量服务',
        current: [8, 12, 6, 15, 9],
        yoy: [10, 10, 10, 10, 10],
        description: '小流量服务的正常波动'
    }
];

// 运行示例
examples.forEach((example, index) => {
    console.log(`${index + 1}. ${example.name}`);
    console.log(`   场景: ${example.description}`);
    
    const result = detectRiskFromWindows(example.current, example.yoy);
    
    console.log(`   当前流量: [${example.current.join(', ')}]`);
    console.log(`   同比流量: [${example.yoy.join(', ')}]`);
    console.log(`   🚨 风险概率: ${(result.risk * 100).toFixed(1)}%`);
    console.log(`   📊 置信度: ${(result.confidence * 100).toFixed(1)}%`);
    console.log(`   🔍 异常类型: ${result.dataRelation.relationType}`);
    
    if (result.dataRelation.flowChangePercent !== undefined) {
        console.log(`   📈 流量变化: ${result.dataRelation.flowChangePercent.toFixed(1)}%`);
    }
    
    if (result.dataRelation.isLowTrafficService) {
        console.log(`   🏷️  小流量服务: 是`);
    }
    
    // 风险级别判断
    let riskLevel = '🟢 正常';
    if (result.risk >= 0.8) riskLevel = '🔴 极高风险';
    else if (result.risk >= 0.7) riskLevel = '🔴 高风险';
    else if (result.risk >= 0.5) riskLevel = '🟠 中高风险';
    else if (result.risk >= 0.3) riskLevel = '🟠 中等风险';
    else if (result.risk >= 0.15) riskLevel = '🟡 需要关注';
    else if (result.risk >= 0.05) riskLevel = '🟡 轻微关注';
    
    console.log(`   ⚡ 风险级别: ${riskLevel}`);
    
    // 建议操作
    if (result.risk >= 0.8) {
        console.log(`   💡 建议: 立即检查系统，可能是攻击或故障`);
    } else if (result.risk >= 0.5) {
        console.log(`   💡 建议: 密切监控，准备应急预案`);
    } else if (result.risk >= 0.15) {
        console.log(`   💡 建议: 持续观察，分析增长原因`);
    } else {
        console.log(`   💡 建议: 正常监控即可`);
    }
    
    console.log();
});

console.log('🎯 使用说明:');
console.log('- current: 当前时间窗口的请求量数组');
console.log('- yoy: 同比时间窗口的请求量数组');
console.log('- 风险概率: 0-100%，越高越危险');
console.log('- 置信度: 检测结果的可信程度');
console.log();

console.log('📚 更多信息请查看 README.md 或运行测试:');
console.log('   npm test');
