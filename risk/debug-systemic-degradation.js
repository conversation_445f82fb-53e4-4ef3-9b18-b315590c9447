/**
 * 调试系统性劣化场景
 */

const { detectLatencyRiskFromWindows } = require('./latency-risk-detector');
const { LatencyTestDataGenerator } = require('./latency-risk-detector.test');

function debugSystemicDegradation() {
    const generator = new LatencyTestDataGenerator();
    
    const baseLatencies = {
        '1': 0.02,      // 平均20ms
        '0.5': 0.015,   // P50: 15ms
        '0.75': 0.025,  // P75: 25ms
        '0.95': 0.05,   // P95: 50ms
        '0.99': 0.1     // P99: 100ms
    };

    const times = generator.generateTimePoints(30);

    console.log('=== 调试系统性劣化场景 ===');
    
    // 系统性劣化：所有分位数渐进式增长3倍
    const degradationCurrent = generator.generateSystemicDegradation(times, baseLatencies, 3);
    const degradationYoy = generator.generateNormalLatency(times, baseLatencies, 0.1);
    
    const result = detectLatencyRiskFromWindows(degradationCurrent, degradationYoy, {
        serviceType: 'MICROSERVICE'
    });
    
    console.log('\n--- 系统性劣化场景分析 ---');
    console.log(`最终风险: ${(result.risk * 100).toFixed(1)}% (期望: 70-100%)`);
    console.log(`置信度: ${(result.confidence * 100).toFixed(1)}%`);
    console.log(`告警数量: ${result.alerts.length} (期望: 3)`);
    
    // 分析各分位数的最终值
    console.log('\n--- 各分位数最终值分析 ---');
    Object.entries(result.percentileRisks).forEach(([percentile, risk]) => {
        const pName = percentile === '1' ? 'AVG' : `P${(parseFloat(percentile) * 100).toFixed(0)}`;
        const expectedFinalValue = baseLatencies[percentile] * 3; // 期望3倍增长
        
        console.log(`${pName}:`);
        console.log(`  期望最终值: ${(expectedFinalValue * 1000).toFixed(1)}ms`);
        console.log(`  实际当前值: ${(risk.currentValue * 1000).toFixed(1)}ms`);
        console.log(`  实际最大值: ${(risk.maxValue * 1000).toFixed(1)}ms`);
        console.log(`  基线值: ${(risk.baselineValue * 1000).toFixed(1)}ms`);
        console.log(`  绝对风险: ${(risk.absoluteRisk * 100).toFixed(1)}%`);
        console.log(`  劣化风险: ${(risk.degradationRisk * 100).toFixed(1)}%`);
        console.log(`  综合风险: ${(risk.combinedRisk * 100).toFixed(1)}%`);
        console.log(`  异常点数: ${risk.anomalyCount}/${risk.pointAnalysis.length}`);
    });
    
    // 分析为什么风险这么低
    console.log('\n--- 风险评分分析 ---');
    console.log(`加权平均风险: ${(result.aggregation.weightedAverageRisk * 100).toFixed(1)}%`);
    console.log(`最高分位数风险: ${(result.aggregation.maxPercentileRisk * 100).toFixed(1)}%`);
    
    // 检查折扣因子
    const totalImprovementCount = Object.values(result.percentileRisks)
        .reduce((sum, risk) => sum + risk.improvementCount, 0);
    const totalAnomalyCount = Object.values(result.percentileRisks)
        .reduce((sum, risk) => sum + risk.anomalyCount, 0);
    const totalPointCount = Object.values(result.percentileRisks)
        .reduce((sum, risk) => sum + risk.pointAnalysis.length, 0);
    
    console.log(`改善点数: ${totalImprovementCount}`);
    console.log(`异常点数: ${totalAnomalyCount}`);
    console.log(`改善率: ${(totalImprovementCount / totalPointCount * 100).toFixed(1)}%`);
    console.log(`异常率: ${(totalAnomalyCount / totalPointCount * 100).toFixed(1)}%`);
    
    // 检查高峰模式
    const peakPatternCount = Object.values(result.percentileRisks)
        .filter(risk => risk.yoyPattern.hasPeakPattern).length;
    console.log(`高峰模式分位数: ${peakPatternCount}`);
    
    // 检查严重耗时问题
    const hasSevereLatencyIssue = Object.values(result.percentileRisks)
        .some(risk => risk.maxValue >= 0.5 || risk.currentValue >= 0.3);
    console.log(`有严重耗时问题: ${hasSevereLatencyIssue}`);
    
    console.log('\n--- 告警详情 ---');
    result.alerts.forEach((alert, i) => {
        console.log(`${i + 1}. [${alert.level}] ${alert.type}: ${alert.message}`);
        if (alert.currentValue) console.log(`   当前值: ${(alert.currentValue * 1000).toFixed(1)}ms`);
        if (alert.maxValue) console.log(`   最大值: ${(alert.maxValue * 1000).toFixed(1)}ms`);
    });
    
    return result;
}

if (require.main === module) {
    debugSystemicDegradation();
}

module.exports = { debugSystemicDegradation };
