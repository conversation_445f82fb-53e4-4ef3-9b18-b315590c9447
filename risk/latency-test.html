<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>响应耗时风险检测算法测试</title>
    <script src="https://smart-static.wosaimg.com/libs/Chart.js/4.5.0/chart.umd.min.js"></script>
    <script src="https://smart-static.wosaimg.com/libs/luxon/3.7.1/luxon.min.js"></script>
    <script src="latency-risk-detector.js?v=1.0.0"></script>
    <style>
        :root {
            --primary: #2c3e50;
            --secondary: #34495e;
            --accent: #3498db;
            --danger: #e74c3c;
            --warning: #f39c12;
            --success: #2ecc71;
            --light: #ecf0f1;
            --dark: #2c3e50;
            --info: #17a2b8;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            margin: 0 auto;
        }

        header {
            text-align: center;
            margin-bottom: 20px;
            padding: 20px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        h1 {
            color: var(--primary);
            margin-bottom: 10px;
            font-size: 2.2rem;
            font-weight: 700;
        }

        .subtitle {
            color: var(--secondary);
            font-size: 1.1rem;
            margin-bottom: 15px;
        }

        .algorithm-info {
            display: flex;
            justify-content: center;
            gap: 30px;
            flex-wrap: wrap;
        }

        .info-item {
            display: flex;
            align-items: center;
            gap: 8px;
            color: var(--secondary);
            font-size: 0.9rem;
        }

        .info-badge {
            background: var(--accent);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .panel {
            background: white;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            padding: 25px;
            margin-bottom: 25px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .panel:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
        }

        .panel-title {
            font-size: 1.4rem;
            color: var(--primary);
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 3px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: 600;
        }

        .panel-title .badge {
            background: var(--accent);
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.85rem;
            font-weight: 500;
        }

        .grid {
            display: grid;
            grid-template-columns: 1fr 2fr 1fr;
            gap: 25px;
        }

        @media (max-width: 1200px) {
            .grid {
                grid-template-columns: 1fr 1fr;
            }
            .grid > div:nth-child(2) {
                order: 1;
            }
            .grid > div:nth-child(1) {
                order: 2;
            }
            .grid > div:nth-child(3) {
                order: 3;
                grid-column: 1 / -1;
            }
        }

        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
            .grid > div {
                order: initial !important;
                grid-column: initial !important;
            }
            .algorithm-info {
                flex-direction: column;
                gap: 15px;
            }
        }

        .input-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--secondary);
            font-size: 0.95rem;
        }

        textarea, input[type="text"], input[type="number"], select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-family: 'Consolas', 'Monaco', monospace;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        textarea:focus, input[type="text"]:focus, input[type="number"]:focus, select:focus {
            border-color: var(--accent);
            outline: none;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        textarea {
            min-height: 120px;
            resize: vertical;
            line-height: 1.5;
        }

        button {
            background: var(--accent);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 0.95rem;
        }

        button:hover:not(:disabled) {
            background: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(52, 152, 219, 0.3);
        }

        button:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .btn-success {
            background: var(--success);
        }

        .btn-success:hover {
            background: #27ae60;
            box-shadow: 0 6px 15px rgba(46, 204, 113, 0.3);
        }

        .btn-warning {
            background: var(--warning);
        }

        .btn-warning:hover {
            background: #e67e22;
            box-shadow: 0 6px 15px rgba(243, 156, 18, 0.3);
        }

        .btn-danger {
            background: var(--danger);
        }

        .btn-danger:hover {
            background: #c0392b;
            box-shadow: 0 6px 15px rgba(231, 76, 60, 0.3);
        }

        .btn-info {
            background: var(--info);
        }

        .btn-info:hover {
            background: #138496;
            box-shadow: 0 6px 15px rgba(23, 162, 184, 0.3);
        }

        .risk-meter {
            text-align: center;
            padding: 25px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            border: 1px solid #dee2e6;
        }

        .risk-value {
            font-size: 3.2rem;
            font-weight: 800;
            margin: 15px 0;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .risk-low {
            color: var(--success);
        }

        .risk-medium {
            color: var(--warning);
        }

        .risk-high {
            color: var(--danger);
        }

        .confidence-bar {
            height: 25px;
            background: #eee;
            border-radius: 12px;
            margin: 20px 0;
            overflow: hidden;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .confidence-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--accent) 0%, #2980b9 100%);
            border-radius: 12px;
            transition: width 0.8s ease;
        }

        .chart-controls {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .view-mode-section, .percentile-section {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            flex-wrap: wrap;
            gap: 10px;
        }

        .percentile-section {
            margin-bottom: 0;
        }

        .view-mode-buttons {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .view-btn {
            padding: 8px 16px;
            border: 2px solid #e9ecef;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
            color: #495057;
        }

        .view-btn:hover {
            border-color: var(--accent);
            color: var(--accent);
            transform: translateY(-1px);
        }

        .view-btn.active {
            background: var(--accent);
            border-color: var(--accent);
            color: white;
            box-shadow: 0 3px 10px rgba(52, 152, 219, 0.3);
        }

        .percentile-toggles {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .toggle-label {
            display: flex;
            align-items: center;
            gap: 6px;
            cursor: pointer;
            padding: 6px 10px;
            border-radius: 6px;
            transition: all 0.3s ease;
            background: white;
            border: 1px solid #e9ecef;
        }

        .toggle-label:hover {
            background: #f8f9fa;
            border-color: #dee2e6;
        }

        .toggle-label input[type="checkbox"] {
            margin: 0;
            cursor: pointer;
        }

        .toggle-text {
            font-weight: 600;
            font-size: 0.9rem;
        }

        .chart-container {
            box-sizing: content-box;
            position: relative;
            margin-bottom: 20px;
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        #latencyChart {
            display: block;
        }

        /* 自定义图例样式 */
        .chart-container canvas {
            /*margin-bottom: 50px;*/
        }

        /* Chart.js 图例样式覆盖 */
        .chart-container .chartjs-legend {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            margin-bottom: 10px;
        }

        .chart-container .chartjs-legend ul {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            margin: 0;
            padding: 0;
            list-style: none;
        }

        .chart-container .chartjs-legend li {
            margin: 3px 8px;
            font-size: 11px;
        }

        /* 自定义图例样式 */
        .chart-legend {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #e9ecef;
        }

        .legend-row {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            margin: 3px 0;
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin: 0 12px;
            font-size: 11px;
            font-weight: 500;
            cursor: pointer;
            padding: 2px 6px;
            border-radius: 3px;
            transition: background-color 0.2s;
        }

        .legend-item:hover {
            background-color: rgba(0,0,0,0.05);
        }

        .legend-item.hidden {
            opacity: 0.3;
        }

        .legend-color {
            width: 12px;
            height: 12px;
            margin-right: 6px;
            border-radius: 2px;
            border: 1px solid rgba(0,0,0,0.1);
        }

        .legend-color.dashed {
            background-image: repeating-linear-gradient(
                90deg,
                transparent,
                transparent 2px,
                currentColor 2px,
                currentColor 4px
            );
            background-color: transparent;
        }

        .percentile-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .percentile-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            border-left: 4px solid var(--accent);
            transition: all 0.3s ease;
        }

        .percentile-card:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }

        .percentile-name {
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 8px;
            font-size: 0.9rem;
        }

        .percentile-value {
            font-size: 1.4rem;
            font-weight: 700;
            color: var(--accent);
            margin-bottom: 5px;
        }

        .percentile-risk {
            font-size: 0.85rem;
            color: #666;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 15px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
            border-left: 5px solid;
        }

        .alert-critical {
            background: #ffebee;
            color: #c62828;
            border-left-color: #c62828;
        }

        .alert-warning {
            background: #fff8e1;
            color: #f57c00;
            border-left-color: #f57c00;
        }

        .alert-info {
            background: #e3f2fd;
            color: #1565c0;
            border-left-color: #1565c0;
        }

        .example-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .example-button {
            padding: 15px;
            text-align: left;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .example-button:hover {
            transform: translateY(-3px);
        }

        .example-title {
            font-weight: 600;
            margin-bottom: 5px;
        }

        .example-desc {
            font-size: 0.85rem;
            opacity: 0.8;
        }

        .config-panel {
            display: none;
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border: 2px solid #e9ecef;
        }

        .config-panel.show {
            display: block;
        }

        .config-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 30px;
        }

        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid var(--accent);
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .business-impact {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .impact-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }

        .impact-level {
            font-weight: 600;
            font-size: 1.1rem;
            margin-bottom: 5px;
        }

        .impact-desc {
            font-size: 0.85rem;
            color: #666;
        }

        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #333;
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
            z-index: 1000;
            max-width: 400px;
            animation: slideIn 0.3s ease;
        }

        .toast.success { background: var(--success); }
        .toast.warning { background: var(--warning); }
        .toast.error { background: var(--danger); }

        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="container">
        <header style="display: none">
            <h1>响应耗时风险检测算法</h1>
            <p class="subtitle">基于多分位数联合分析的智能耗时异常检测系统</p>
            <div class="algorithm-info">
                <div class="info-item">
                    <span>🎯</span>
                    <span>单向异常检测</span>
                </div>
                <div class="info-item">
                    <span>📊</span>
                    <span>多分位数分析</span>
                </div>
                <div class="info-item">
                    <span>🧠</span>
                    <span>同比模式学习</span>
                </div>
                <div class="info-item">
                    <span>⚡</span>
                    <span class="info-badge">v2.0</span>
                </div>
            </div>
        </header>

        <div class="grid">
            <!-- 左侧：数据输入和配置 -->
            <div class="panel">
                <h2 class="panel-title">
                    数据输入
                    <span class="badge">多分位数格式</span>
                </h2>

                <div class="input-group">
                    <label for="currentLatency">当前耗时数据 (JSON格式)</label>
                    <textarea id="currentLatency" placeholder='[
  {"time": 1757179920, "value": "0.025", "category": "1"},
  {"time": 1757179920, "value": "0.055", "category": "0.95"},
  {"time": 1757179920, "value": "0.120", "category": "0.99"}
]'></textarea>
                    <div style="font-size: 0.85rem; color: #666; margin-top: 5px;">
                        格式说明：category为分位数标识 (1=平均值, 0.99=P99, 0.95=P95, 0.75=P75, 0.5=P50)
                    </div>
                </div>

                <div class="input-group">
                    <label for="yoyLatency">同比耗时数据 (JSON格式)</label>
                    <textarea id="yoyLatency" placeholder='[
  {"time": 1757179920, "value": "0.022", "category": "1"},
  {"time": 1757179920, "value": "0.050", "category": "0.95"},
  {"time": 1757179920, "value": "0.110", "category": "0.99"}
]'></textarea>
                </div>

                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; flex-wrap: wrap; gap: 10px;">
                    <div style="display: flex; gap: 10px;">
                        <button id="analyzeBtn" class="btn-success">🔍 开始分析</button>
                        <button id="shareBtn" class="btn-info" title="生成分享链接" style="display: none;">🔗 分享</button>
                    </div>
                    <button id="configToggle" class="btn-info">⚙️ 高级配置</button>
                </div>

                <div id="configPanel" class="config-panel">
                    <div class="config-grid">
                        <div class="input-group">
                            <label for="serviceType">服务类型</label>
                            <select id="serviceType">
                                <option value="MICROSERVICE">微服务</option>
                                <option value="DATABASE">数据库</option>
                                <option value="API_GATEWAY">API网关</option>
                                <option value="WEB_API">Web API</option>
                                <option value="BATCH_JOB">批处理</option>
                            </select>
                        </div>

                        <div class="input-group">
                            <label for="zThreshold">Z阈值</label>
                            <input type="number" id="zThreshold" value="1.8" step="0.1" min="1.0" max="3.0">
                        </div>

                        <div class="input-group">
                            <label for="degradationThreshold">劣化阈值</label>
                            <input type="number" id="degradationThreshold" value="0.2" step="0.05" min="0.1" max="0.5">
                        </div>

                        <div class="input-group">
                            <label for="enablePatternLearning">模式学习</label>
                            <select id="enablePatternLearning">
                                <option value="true">启用</option>
                                <option value="false">禁用</option>
                            </select>
                        </div>
                    </div>
                </div>

                <h3 class="panel-title">测试场景</h3>
                <div class="example-grid">
                    <button class="example-button btn-success" data-example="normal">
                        <div class="example-title">正常稳定</div>
                        <div class="example-desc">各分位数稳定运行</div>
                    </button>
                    <button class="example-button btn-warning" data-example="spike">
                        <div class="example-title">P99突发尖刺</div>
                        <div class="example-desc">P99短时间大幅上升</div>
                    </button>
                    <button class="example-button btn-danger" data-example="degradation">
                        <div class="example-title">系统性劣化</div>
                        <div class="example-desc">所有分位数同步上升</div>
                    </button>
                    <button class="example-button btn-warning" data-example="tail">
                        <div class="example-title">长尾恶化</div>
                        <div class="example-desc">P99上升但平均值稳定</div>
                    </button>
                    <button class="example-button btn-info" data-example="peak">
                        <div class="example-title">高峰时段</div>
                        <div class="example-desc">业务高峰期模式</div>
                    </button>
                    <button class="example-button btn-success" data-example="improvement">
                        <div class="example-title">性能改善</div>
                        <div class="example-desc">各分位数下降</div>
                    </button>
                </div>
            </div>

            <!-- 中间：可视化图表 -->
            <div class="panel">
                <h2 class="panel-title">
                    数据可视化
                    <span class="badge">多分位数趋势</span>
                </h2>
                
                <!-- 图表控制面板 -->
                <div class="chart-controls">
                    <div class="view-mode-section">
                        <label style="font-weight: 600; margin-right: 15px;">视图模式:</label>
                        <div class="view-mode-buttons">
                            <button class="view-btn active" data-mode="current">仅当前</button>
                            <button class="view-btn" data-mode="yoy">仅同比</button>
                            <button class="view-btn" data-mode="comparison">对比视图</button>
                            <button class="view-btn" data-mode="difference">差值视图</button>
                        </div>
                    </div>

                    <div class="percentile-section">
                        <label style="font-weight: 600; margin-right: 15px;">显示分位数:</label>
                        <div class="percentile-toggles">
                            <label class="toggle-label">
                                <input type="checkbox" checked data-percentile="p99">
                                <span class="toggle-text" style="color: #e74c3c;">P99</span>
                            </label>
                            <label class="toggle-label">
                                <input type="checkbox" checked data-percentile="p95">
                                <span class="toggle-text" style="color: #f39c12;">P95</span>
                            </label>
                            <label class="toggle-label">
                                <input type="checkbox" checked data-percentile="p75">
                                <span class="toggle-text" style="color: #3498db;">P75</span>
                            </label>
                            <label class="toggle-label">
                                <input type="checkbox" checked data-percentile="p50">
                                <span class="toggle-text" style="color: #2ecc71;">P50</span>
                            </label>
                            <label class="toggle-label">
                                <input type="checkbox" checked data-percentile="avg">
                                <span class="toggle-text" style="color: #9b59b6;">AVG</span>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="chart-container">
                    <div id="legend-container" class="chart-legend"></div>
                    <canvas id="latencyChart" width="800" height="400"></canvas>
                </div>

                <div class="loading" id="chartLoading">
                    <div class="loading-spinner"></div>
                    <p>正在分析耗时数据...</p>
                </div>

                <div class="percentile-grid" id="percentileGrid">
                    <!-- 分位数卡片将在这里动态生成 -->
                </div>
            </div>

            <!-- 右侧：风险结果和告警 -->
            <div class="panel">
                <h2 class="panel-title">
                    风险评估
                    <span class="badge">智能分析</span>
                </h2>

                <div class="risk-meter">
                    <div style="font-size: 1.1rem; font-weight: 600; margin-bottom: 10px;">综合风险概率</div>
                    <div id="riskValue" class="risk-value">-</div>
                    <div id="riskLevel" class="risk-level">等待分析...</div>

                    <div style="margin-top: 20px;">
                        <div style="font-weight: 600; margin-bottom: 10px;">置信度</div>
                        <div class="confidence-bar">
                            <div id="confidenceFill" class="confidence-fill" style="width: 0%;"></div>
                        </div>
                        <div id="confidenceValue">未计算</div>
                    </div>
                </div>

                <h3 class="panel-title" style="margin-top: 25px;">业务影响评估</h3>
                <div class="business-impact" id="businessImpact">
                    <div class="impact-item">
                        <div class="impact-level" id="userExperience">-</div>
                        <div class="impact-desc">用户体验</div>
                    </div>
                    <div class="impact-item">
                        <div class="impact-level" id="systemStability">-</div>
                        <div class="impact-desc">系统稳定性</div>
                    </div>
                    <div class="impact-item">
                        <div class="impact-level" id="businessContinuity">-</div>
                        <div class="impact-desc">业务连续性</div>
                    </div>
                </div>

                <h3 class="panel-title" style="margin-top: 25px;">智能告警</h3>
                <div id="alertsContainer">
                    <p style="color: #666; text-align: center; padding: 20px;">等待分析结果...</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let latencyChart = null;
        let currentResult = null;
        let currentData = null;
        let yoyData = null;
        let currentViewMode = 'current';

        // ========== 数据压缩/解压缩功能 ==========

        function ultraCompressLatencyData(data) {
            if (!data || data.length === 0) return '';

            try {
                const baseTime = data[0].time;
                const interval = 60; // 假设固定60秒间隔

                // 按时间分组
                const timeGroups = {};
                data.forEach(item => {
                    const timeIndex = Math.round((item.time - baseTime) / interval);
                    if (!timeGroups[timeIndex]) timeGroups[timeIndex] = {};
                    timeGroups[timeIndex][item.category] = parseFloat(item.value);
                });

                // 构建压缩字符串
                let compressed = '';
                compressed += baseTime.toString(36) + '|';
                compressed += interval.toString(36) + '|';

                const categories = ['1', '0.5', '0.75', '0.95', '0.99'];
                const timeIndices = Object.keys(timeGroups).map(Number).sort((a, b) => a - b);

                const dataGroups = timeIndices.map(timeIndex => {
                    const group = timeGroups[timeIndex];

                    // 每个时间点的5个值压缩
                    const values = categories.map(cat => {
                        const val = group[cat] || 0;
                        // 量化：0-10秒映射到0-65535
                        const quantized = Math.min(65535, Math.max(0, Math.round(val * 6553.5)));
                        return quantized;
                    });

                    // 转为36进制字符串
                    return values.map(v => v.toString(36)).join('.');
                });

                compressed += dataGroups.join(',');
                return compressed;

            } catch (error) {
                console.error('压缩失败:', error);
                return '';
            }
        }

        function ultraDecompressLatencyData(compressedStr) {
            if (!compressedStr) return [];

            try {
                const parts = compressedStr.split('|');
                if (parts.length < 3) {
                    throw new Error('压缩数据格式错误');
                }

                const baseTime = parseInt(parts[0], 36);
                const interval = parseInt(parts[1], 36);
                const dataStr = parts[2];

                if (isNaN(baseTime) || isNaN(interval)) {
                    throw new Error('时间参数解析失败');
                }

                // 解析数据点
                const dataGroups = dataStr.split(',');
                const categories = ['1', '0.5', '0.75', '0.95', '0.99'];
                const result = [];

                dataGroups.forEach((group, timeIndex) => {
                    if (!group) return;

                    const values = group.split('.');
                    if (values.length !== 5) {
                        console.warn(`时间点 ${timeIndex} 的数据不完整`);
                        return;
                    }

                    const time = baseTime + timeIndex * interval;

                    values.forEach((valueStr, catIndex) => {
                        try {
                            const quantizedValue = parseInt(valueStr, 36);
                            if (isNaN(quantizedValue)) return;

                            // 反量化：从0-65535映射回0-10秒
                            const originalValue = quantizedValue / 6553.5;

                            result.push({
                                time: time,
                                value: originalValue.toFixed(6),
                                category: categories[catIndex]
                            });
                        } catch (error) {
                            console.warn(`解析数值失败: ${valueStr}`, error);
                        }
                    });
                });

                return result;

            } catch (error) {
                console.error('解压缩失败:', error);
                return [];
            }
        }

        // 图表配置
        const chartConfig = {
            colors: {
                'P99': '#e74c3c',
                'P95': '#f39c12',
                'P75': '#3498db',
                'P50': '#2ecc71',
                'AVG': '#9b59b6'
            },
            styles: {
                current: { borderWidth: 1, tension: 0.3, fill: false },
                yoy: { borderWidth: 1, tension: 0.3, fill: false, borderDash: [2, 2] },
                difference: { borderWidth: 1, tension: 0.3, fill: true }
            }
        };

        // 示例数据
        const examples = {
            normal: {
                current: generateLatencyData(30, {
                    '1': 0.02, '0.5': 0.015, '0.75': 0.025, '0.95': 0.05, '0.99': 0.1
                }, 0.1),
                yoy: generateLatencyData(30, {
                    '1': 0.02, '0.5': 0.015, '0.75': 0.025, '0.95': 0.05, '0.99': 0.1
                }, 0.1)
            },
            spike: {
                current: generateSpikeData(30, {
                    '1': 0.02, '0.5': 0.015, '0.75': 0.025, '0.95': 0.05, '0.99': 0.1
                }, 20, 25, 8),
                yoy: generateLatencyData(30, {
                    '1': 0.02, '0.5': 0.015, '0.75': 0.025, '0.95': 0.05, '0.99': 0.1
                }, 0.1)
            },
            degradation: {
                current: generateDegradationData(30, {
                    '1': 0.02, '0.5': 0.015, '0.75': 0.025, '0.95': 0.05, '0.99': 0.1
                }, 3),
                yoy: generateLatencyData(30, {
                    '1': 0.02, '0.5': 0.015, '0.75': 0.025, '0.95': 0.05, '0.99': 0.1
                }, 0.1)
            },
            tail: {
                current: generateTailDegradationData(30, {
                    '1': 0.02, '0.5': 0.015, '0.75': 0.025, '0.95': 0.05, '0.99': 0.1
                }, 4),
                yoy: generateLatencyData(30, {
                    '1': 0.02, '0.5': 0.015, '0.75': 0.025, '0.95': 0.05, '0.99': 0.1
                }, 0.1)
            },
            peak: {
                current: generatePeakData(30, {
                    '1': 0.02, '0.5': 0.015, '0.75': 0.025, '0.95': 0.05, '0.99': 0.1
                }, 2.0),
                yoy: generatePeakData(30, {
                    '1': 0.02, '0.5': 0.015, '0.75': 0.025, '0.95': 0.05, '0.99': 0.1
                }, 1.8)
            },
            improvement: {
                current: generateImprovementData(30, {
                    '1': 0.02, '0.5': 0.015, '0.75': 0.025, '0.95': 0.05, '0.99': 0.1
                }, 0.6),
                yoy: generateLatencyData(30, {
                    '1': 0.02, '0.5': 0.015, '0.75': 0.025, '0.95': 0.05, '0.99': 0.1
                }, 0.1)
            }
        };

        // 数据生成函数
        function generateLatencyData(count, baseLatencies, noise = 0.1) {
            const data = [];
            const baseTime = 1757179920;
            
            for (let i = 0; i < count; i++) {
                const time = baseTime + i * 60;
                Object.entries(baseLatencies).forEach(([category, baseValue]) => {
                    const noiseValue = baseValue * (1 + (Math.random() - 0.5) * noise);
                    data.push({
                        time: time,
                        value: Math.max(0.001, noiseValue).toFixed(6),
                        category: category
                    });
                });
            }
            return data;
        }

        function generateSpikeData(count, baseLatencies, spikeStart, spikeEnd, multiplier) {
            const data = [];
            const baseTime = 1757179920;
            
            for (let i = 0; i < count; i++) {
                const time = baseTime + i * 60;
                const isInSpike = i >= spikeStart && i <= spikeEnd;
                
                Object.entries(baseLatencies).forEach(([category, baseValue]) => {
                    let value = baseValue;
                    if (isInSpike) {
                        const spikeMultiplier = category === '0.99' ? multiplier :
                                              category === '0.95' ? multiplier * 0.7 :
                                              category === '0.75' ? multiplier * 0.3 :
                                              multiplier * 0.1;
                        value = baseValue * spikeMultiplier;
                    }
                    
                    data.push({
                        time: time,
                        value: Math.max(0.001, value).toFixed(6),
                        category: category
                    });
                });
            }
            return data;
        }

        function generateDegradationData(count, baseLatencies, factor) {
            const data = [];
            const baseTime = 1757179920;
            
            for (let i = 0; i < count; i++) {
                const time = baseTime + i * 60;
                const progressiveFactor = 1 + (factor - 1) * (i / count);
                
                Object.entries(baseLatencies).forEach(([category, baseValue]) => {
                    const value = baseValue * progressiveFactor;
                    data.push({
                        time: time,
                        value: Math.max(0.001, value).toFixed(6),
                        category: category
                    });
                });
            }
            return data;
        }

        function generateTailDegradationData(count, baseLatencies, multiplier) {
            const data = [];
            const baseTime = 1757179920;
            
            for (let i = 0; i < count; i++) {
                const time = baseTime + i * 60;
                
                Object.entries(baseLatencies).forEach(([category, baseValue]) => {
                    let value = baseValue;
                    if (category === '0.99') {
                        value = baseValue * multiplier;
                    } else if (category === '0.95') {
                        value = baseValue * (1 + (multiplier - 1) * 0.5);
                    }
                    
                    data.push({
                        time: time,
                        value: Math.max(0.001, value).toFixed(6),
                        category: category
                    });
                });
            }
            return data;
        }

        function generatePeakData(count, baseLatencies, multiplier) {
            const data = [];
            const baseTime = 1757179920;
            const peakStart = Math.floor(count * 0.3);
            const peakEnd = Math.floor(count * 0.7);
            
            for (let i = 0; i < count; i++) {
                const time = baseTime + i * 60;
                const isInPeak = i >= peakStart && i <= peakEnd;
                
                Object.entries(baseLatencies).forEach(([category, baseValue]) => {
                    let value = baseValue;
                    if (isInPeak) {
                        const peakMultiplier = category === '0.99' ? multiplier :
                                             category === '0.95' ? multiplier * 0.8 :
                                             category === '0.75' ? multiplier * 0.5 :
                                             multiplier * 0.3;
                        value = baseValue * peakMultiplier;
                    }
                    
                    data.push({
                        time: time,
                        value: Math.max(0.001, value).toFixed(6),
                        category: category
                    });
                });
            }
            return data;
        }

        function generateImprovementData(count, baseLatencies, factor) {
            const data = [];
            const baseTime = 1757179920;
            
            for (let i = 0; i < count; i++) {
                const time = baseTime + i * 60;
                const progressiveFactor = 1 - (1 - factor) * (i / count);
                
                Object.entries(baseLatencies).forEach(([category, baseValue]) => {
                    const value = baseValue * progressiveFactor;
                    data.push({
                        time: time,
                        value: Math.max(0.001, value).toFixed(6),
                        category: category
                    });
                });
            }
            return data;
        }

        // ========== URL参数处理功能 ==========

        function parseUrlParams() {
            const params = new URLSearchParams(window.location.search);

            // 1. 检查压缩数据参数
            if (params.has('data')) {
                const currentCompressed = params.get('data');
                const yoyCompressed = params.get('yoy') || currentCompressed; // 如果没有yoy，使用相同数据

                try {
                    const currentData = ultraDecompressLatencyData(currentCompressed);
                    const yoyData = ultraDecompressLatencyData(yoyCompressed);

                    if (currentData.length > 0 && yoyData.length > 0) {
                        return {
                            type: 'compressed',
                            currentData: currentData,
                            yoyData: yoyData,
                            viewMode: params.get('view') || 'current',
                            config: {
                                serviceType: params.get('service') || 'MICROSERVICE',
                                z_th: parseFloat(params.get('z_th')) || 1.8,
                                degradationThreshold: parseFloat(params.get('deg_th')) || 0.2,
                                enablePatternLearning: params.get('pattern') !== 'false'
                            }
                        };
                    }
                } catch (error) {
                    console.error('解析压缩数据失败:', error);
                    showToast('URL参数中的数据格式错误', 'error');
                }
            }

            // 2. 检查场景参数
            if (params.has('scenario')) {
                const scenario = params.get('scenario');
                const intensity = parseFloat(params.get('intensity')) || getDefaultIntensity(scenario);
                const duration = parseInt(params.get('duration')) || getDefaultDuration(scenario);
                const service = params.get('service') || 'MICROSERVICE';
                const viewMode = params.get('view') || 'current';

                return {
                    type: 'scenario',
                    scenario: scenario,
                    intensity: intensity,
                    duration: duration,
                    service: service,
                    viewMode: viewMode
                };
            }

            return null;
        }

        function getDefaultIntensity(scenario) {
            const defaults = {
                'normal': 1,
                'spike': 8,
                'degradation': 3,
                'tail': 4,
                'peak': 2,
                'improvement': 0.6
            };
            return defaults[scenario] || 1;
        }

        function getDefaultDuration(scenario) {
            const defaults = {
                'normal': 0,
                'spike': 5,
                'degradation': 30,
                'tail': 30,
                'peak': 15,
                'improvement': 30
            };
            return defaults[scenario] || 0;
        }

        function loadDataFromParams(paramData) {
            if (paramData.type === 'compressed') {
                // 加载压缩数据
                document.getElementById('currentLatency').value = JSON.stringify(paramData.currentData, null, 2);
                document.getElementById('yoyLatency').value = JSON.stringify(paramData.yoyData, null, 2);

                // 设置配置
                document.getElementById('serviceType').value = paramData.config.serviceType;
                document.getElementById('zThreshold').value = paramData.config.z_th;
                document.getElementById('degradationThreshold').value = paramData.config.degradationThreshold;
                document.getElementById('enablePatternLearning').value = paramData.config.enablePatternLearning.toString();

                // 设置视图模式
                currentViewMode = paramData.viewMode;
                document.querySelectorAll('.view-btn').forEach(btn => {
                    btn.classList.toggle('active', btn.getAttribute('data-mode') === paramData.viewMode);
                });

                // 存储数据
                currentData = paramData.currentData;
                yoyData = paramData.yoyData;

                // 自动分析
                setTimeout(() => analyzeData(), 500);

                showToast('已从URL加载自定义数据', 'success');

            } else if (paramData.type === 'scenario') {
                // 加载场景数据
                loadExample(paramData.scenario);

                // 设置视图模式
                if (paramData.viewMode !== 'current') {
                    setTimeout(() => {
                        currentViewMode = paramData.viewMode;
                        document.querySelectorAll('.view-btn').forEach(btn => {
                            btn.classList.toggle('active', btn.getAttribute('data-mode') === paramData.viewMode);
                        });
                        updateChartData();
                    }, 1000);
                }

                showToast(`已加载场景: ${paramData.scenario}`, 'info');
            }
        }

        // DOM加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initChart();
            bindEvents();

            // 检查URL参数
            const paramData = parseUrlParams();
            if (paramData) {
                loadDataFromParams(paramData);
            } else {
                loadExample('normal'); // 默认加载正常场景
            }
        });

        // 初始化图表
        function initChart() {
            if (latencyChart) {
                latencyChart.destroy();
            }

            const ctx = document.getElementById('latencyChart').getContext('2d');
            latencyChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: []
                },
                options: {
                    responsive: false,
                    maintainAspectRatio: false,
                    interaction: {
                        mode: 'index',
                        intersect: false,
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '响应时间 (秒)',
                                font: { weight: 'bold', size: 14 }
                            },
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        },
                        x: {
                            title: {
                                display: false,
                                text: '时间',
                                font: { weight: 'bold', size: 14 }
                            },
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            backgroundColor: 'rgba(0,0,0,0.8)',
                            titleColor: 'white',
                            bodyColor: 'white',
                            borderColor: 'rgba(255,255,255,0.2)',
                            borderWidth: 1,
                            callbacks: {
                                label: function(context) {
                                    const value = context.parsed.y;
                                    if (currentViewMode === 'difference') {
                                        return `${context.dataset.label}: ${value > 0 ? '+' : ''}${value.toFixed(1)}%`;
                                    } else {
                                        return `${context.dataset.label}: ${(value * 1000).toFixed(1)}ms`;
                                    }
                                }
                            }
                        },
                        legend: {
                            display: false  // 禁用默认图例，使用自定义图例
                        },
                        htmlLegend: {
                            containerID: 'legend-container'
                        }
                    },
                    elements: {
                        point: {
                            radius: 0,
                            hoverRadius: 4
                        }
                    }
                }
            });
        }

        // 绑定事件
        function bindEvents() {
            document.getElementById('analyzeBtn').addEventListener('click', analyzeData);

            document.getElementById('configToggle').addEventListener('click', function() {
                const panel = document.getElementById('configPanel');
                const isShowing = panel.classList.contains('show');

                if (isShowing) {
                    panel.classList.remove('show');
                    this.innerHTML = '⚙️ 高级配置';
                } else {
                    panel.classList.add('show');
                    this.innerHTML = '⚙️ 隐藏配置';
                }
            });

            document.querySelectorAll('.example-button').forEach(button => {
                button.addEventListener('click', function() {
                    const exampleType = this.getAttribute('data-example');
                    loadExample(exampleType);
                });
            });

            // 视图模式切换
            document.querySelectorAll('.view-btn').forEach(button => {
                button.addEventListener('click', function() {
                    // 更新按钮状态
                    document.querySelectorAll('.view-btn').forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');

                    // 切换视图模式
                    const mode = this.getAttribute('data-mode');
                    switchViewMode(mode);
                });
            });

            // 分位数显示切换
            document.querySelectorAll('.percentile-toggles input[type="checkbox"]').forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    updateChartVisibility();
                });
            });
        }

        // 切换视图模式
        function switchViewMode(mode) {
            currentViewMode = mode;

            if (currentData && yoyData) {
                updateChartData();
            }
        }

        // 更新图表可见性
        function updateChartVisibility() {
            if (!latencyChart) return;

            const checkboxes = document.querySelectorAll('.percentile-toggles input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                const percentile = checkbox.getAttribute('data-percentile');
                const isChecked = checkbox.checked;

                // 查找对应的数据集
                latencyChart.data.datasets.forEach(dataset => {
                    if (dataset.label.toLowerCase().includes(percentile)) {
                        dataset.hidden = !isChecked;
                    }
                });
            });

            latencyChart.update();

            // 同步自定义图例状态
            const legendItems = document.querySelectorAll('.legend-item');
            legendItems.forEach(item => {
                const datasetIndex = parseInt(item.getAttribute('data-dataset-index'));
                const dataset = latencyChart.data.datasets[datasetIndex];
                if (dataset) {
                    item.classList.toggle('hidden', dataset.hidden);
                }
            });
        }

        // 加载示例数据
        function loadExample(type) {
            const example = examples[type];
            if (!example) return;

            document.getElementById('currentLatency').value = JSON.stringify(example.current, null, 2);
            document.getElementById('yoyLatency').value = JSON.stringify(example.yoy, null, 2);

            // 存储数据
            currentData = example.current;
            yoyData = example.yoy;

            // 自动分析
            setTimeout(() => analyzeData(), 100);
        }

        // 分析数据
        function analyzeData() {
            const parsedData = parseInputData();

            if (!parsedData) {
                showToast('请输入当前和同比耗时数据', 'warning');
                return;
            }

            try {
                // 显示加载状态
                document.getElementById('chartLoading').style.display = 'block';

                // 获取配置
                const config = {
                    serviceType: document.getElementById('serviceType').value,
                    z_th: parseFloat(document.getElementById('zThreshold').value),
                    degradationThreshold: parseFloat(document.getElementById('degradationThreshold').value),
                    enablePatternLearning: document.getElementById('enablePatternLearning').value === 'true'
                };

                // 禁用分析按钮
                const analyzeBtn = document.getElementById('analyzeBtn');
                analyzeBtn.disabled = true;
                analyzeBtn.textContent = '🔄 分析中...';

                setTimeout(() => {
                    try {
                        currentResult = detectLatencyRiskFromWindows(parsedData.currentData, parsedData.yoyData, config);
                        updateUI(currentResult);
                        updateChartData(); // 更新图表数据
                        showToast('分析完成！', 'success');
                    } catch (error) {
                        console.error('算法执行错误:', error);
                        showToast('算法执行出错: ' + error.message, 'error');
                    } finally {
                        document.getElementById('chartLoading').style.display = 'none';
                        analyzeBtn.disabled = false;
                        analyzeBtn.textContent = '🔍 开始分析';
                    }
                }, 100);

            } catch (error) {
                showToast('数据处理错误: ' + error.message, 'error');
            }
        }

        // 更新UI
        function updateUI(result) {
            // 更新风险值
            const riskPercent = (result.risk * 100).toFixed(1) + '%';
            document.getElementById('riskValue').textContent = riskPercent;

            // 设置风险级别样式
            const riskLevel = document.getElementById('riskLevel');
            const riskValue = document.getElementById('riskValue');
            
            if (result.risk > 0.7) {
                riskLevel.textContent = '高风险';
                riskLevel.className = 'risk-level';
                riskValue.className = 'risk-value risk-high';
            } else if (result.risk > 0.4) {
                riskLevel.textContent = '中风险';
                riskLevel.className = 'risk-level';
                riskValue.className = 'risk-value risk-medium';
            } else {
                riskLevel.textContent = '低风险';
                riskLevel.className = 'risk-level';
                riskValue.className = 'risk-value risk-low';
            }

            // 更新置信度
            const confidencePercent = (result.confidence * 100).toFixed(1) + '%';
            document.getElementById('confidenceFill').style.width = confidencePercent;
            document.getElementById('confidenceValue').textContent = confidencePercent;

            // 更新分位数卡片
            updatePercentileCards(result);

            // 更新业务影响
            updateBusinessImpact(result);

            // 更新告警
            updateAlerts(result);
        }

        // 解析输入数据
        function parseInputData() {
            try {
                const currentInput = document.getElementById('currentLatency').value;
                const yoyInput = document.getElementById('yoyLatency').value;

                if (!currentInput || !yoyInput) {
                    return null;
                }

                currentData = JSON.parse(currentInput);
                yoyData = JSON.parse(yoyInput);

                return { currentData, yoyData };
            } catch (error) {
                console.error('数据解析错误:', error);
                return null;
            }
        }

        // 按分位数分组数据
        function groupDataByPercentile(data) {
            const grouped = {};
            const timeLabels = new Set();

            data.forEach(item => {
                const percentile = item.category;
                if (!grouped[percentile]) {
                    grouped[percentile] = [];
                }
                grouped[percentile].push({
                    time: item.time,
                    value: parseFloat(item.value)
                });
                timeLabels.add(item.time);
            });

            // 排序时间点
            Object.keys(grouped).forEach(percentile => {
                grouped[percentile].sort((a, b) => a.time - b.time);
            });

            return { grouped, timeLabels: Array.from(timeLabels).sort((a, b) => a - b) };
        }

        // 计算差值数据
        function calculateDifferenceData(current, yoy) {
            const result = {};

            Object.keys(current).forEach(percentile => {
                if (yoy[percentile]) {
                    const currentValues = current[percentile];
                    const yoyValues = yoy[percentile];

                    result[percentile] = currentValues.map((curr, index) => {
                        const yoyVal = yoyValues[index];
                        if (yoyVal && yoyVal.value > 0) {
                            const diff = ((curr.value - yoyVal.value) / yoyVal.value) * 100;
                            return {
                                time: curr.time,
                                value: diff,
                                isImprovement: diff < 0,
                                isDegradation: diff > 20
                            };
                        }
                        return { time: curr.time, value: 0 };
                    });
                }
            });

            return result;
        }

        // 生成自定义图例
        function generateCustomLegend(datasets) {
            const legendContainer = document.getElementById('legend-container');
            if (!legendContainer) return;

            // 分组数据集
            const currentDatasets = datasets.filter(d => d.label.includes('(当前)'));
            const yoyDatasets = datasets.filter(d => d.label.includes('(同比)'));
            const otherDatasets = datasets.filter(d => !d.label.includes('(当前)') && !d.label.includes('(同比)'));

            let html = '';

            // 当前数据行
            if (currentDatasets.length > 0) {
                html += '<div class="legend-row">';
                currentDatasets.forEach((dataset, index) => {
                    const isHidden = dataset.hidden || false;
                    html += `
                        <div class="legend-item ${isHidden ? 'hidden' : ''}" data-dataset-index="${datasets.indexOf(dataset)}">
                            <div class="legend-color" style="background-color: ${dataset.borderColor};"></div>
                            <span>${dataset.label}</span>
                        </div>
                    `;
                });
                html += '</div>';
            }

            // 同比数据行
            if (yoyDatasets.length > 0) {
                html += '<div class="legend-row">';
                yoyDatasets.forEach((dataset, index) => {
                    const isHidden = dataset.hidden || false;
                    html += `
                        <div class="legend-item ${isHidden ? 'hidden' : ''}" data-dataset-index="${datasets.indexOf(dataset)}">
                            <div class="legend-color dashed" style="color: ${dataset.borderColor};"></div>
                            <span>${dataset.label}</span>
                        </div>
                    `;
                });
                html += '</div>';
            }

            // 其他数据行
            if (otherDatasets.length > 0) {
                html += '<div class="legend-row">';
                otherDatasets.forEach((dataset, index) => {
                    const isHidden = dataset.hidden || false;
                    html += `
                        <div class="legend-item ${isHidden ? 'hidden' : ''}" data-dataset-index="${datasets.indexOf(dataset)}">
                            <div class="legend-color" style="background-color: ${dataset.borderColor};"></div>
                            <span>${dataset.label}</span>
                        </div>
                    `;
                });
                html += '</div>';
            }

            legendContainer.innerHTML = html;

            // 添加点击事件
            legendContainer.querySelectorAll('.legend-item').forEach(item => {
                item.addEventListener('click', function() {
                    const datasetIndex = parseInt(this.getAttribute('data-dataset-index'));
                    const dataset = latencyChart.data.datasets[datasetIndex];

                    if (dataset) {
                        dataset.hidden = !dataset.hidden;
                        this.classList.toggle('hidden', dataset.hidden);
                        latencyChart.update();
                    }
                });
            });
        }

        // 更新图表数据
        function updateChartData() {
            if (!currentData || !yoyData || !latencyChart) return;

            const { grouped: currentGrouped, timeLabels } = groupDataByPercentile(currentData);
            const { grouped: yoyGrouped } = groupDataByPercentile(yoyData);

            const labels = timeLabels.map(time => formatTime(time));
            const datasets = [];

            const percentileMap = {
                '0.99': 'P99',
                '0.95': 'P95',
                '0.75': 'P75',
                '0.5': 'P50',
                '1': 'AVG'
            };

            if (currentViewMode === 'current' || currentViewMode === 'comparison') {
                // 添加当前数据 - 按顺序添加
                const currentOrder = ['P99', 'P95', 'P75', 'P50', 'AVG'];
                currentOrder.forEach(label => {
                    const key = Object.keys(percentileMap).find(k => percentileMap[k] === label);
                    if (key && currentGrouped[key]) {
                        datasets.push(createDataset(
                            label + ' (当前)',
                            currentGrouped[key].map(item => item.value),
                            chartConfig.colors[label],
                            chartConfig.styles.current
                        ));
                    }
                });
            }

            if (currentViewMode === 'yoy' || currentViewMode === 'comparison') {
                // 添加同比数据 - 按顺序添加
                const yoyOrder = ['P99', 'P95', 'P75', 'P50', 'AVG'];
                yoyOrder.forEach(label => {
                    const key = Object.keys(percentileMap).find(k => percentileMap[k] === label);
                    if (key && yoyGrouped[key]) {
                        const style = { ...chartConfig.styles.yoy };
                        style.backgroundColor = chartConfig.colors[label] + '20'; // 20% 透明度

                        datasets.push(createDataset(
                            label + ' (同比)',
                            yoyGrouped[key].map(item => item.value),
                            chartConfig.colors[label],
                            style
                        ));
                    }
                });
            }

            if (currentViewMode === 'difference') {
                // 添加差值数据
                const diffData = calculateDifferenceData(currentGrouped, yoyGrouped);
                Object.entries(percentileMap).forEach(([key, label]) => {
                    if (diffData[key]) {
                        const style = { ...chartConfig.styles.difference };
                        style.backgroundColor = chartConfig.colors[label] + '30'; // 30% 透明度

                        datasets.push(createDataset(
                            label + ' (差值%)',
                            diffData[key].map(item => item.value),
                            chartConfig.colors[label],
                            style
                        ));
                    }
                });

                // 更新Y轴标题
                latencyChart.options.scales.y.title.text = '变化百分比 (%)';
            } else {
                latencyChart.options.scales.y.title.text = '响应时间 (秒)';
            }

            latencyChart.data.labels = labels;
            latencyChart.data.datasets = datasets;
            latencyChart.update();

            // 生成自定义图例
            generateCustomLegend(datasets);

            // 更新可见性
            updateChartVisibility();
        }

        // 创建数据集
        function createDataset(label, data, color, style) {
            return {
                label: label,
                data: data,
                borderColor: color,
                backgroundColor: style.backgroundColor || color + '20',
                borderWidth: style.borderWidth,
                tension: style.tension,
                fill: style.fill,
                borderDash: style.borderDash || [],
                pointRadius: 0,
                pointHoverRadius: 4,
                pointBackgroundColor: color,
                pointBorderColor: color,
                pointHoverBackgroundColor: color,
                pointHoverBorderColor: '#fff',
                pointHoverBorderWidth: 2
            };
        }



        // 更新分位数卡片
        function updatePercentileCards(result) {
            const container = document.getElementById('percentileGrid');
            let html = '';

            const percentileNames = {
                '0.99': 'P99',
                '0.95': 'P95', 
                '0.75': 'P75',
                '0.5': 'P50',
                '1': 'AVG'
            };

            Object.entries(result.percentileRisks || {}).forEach(([percentile, risk]) => {
                const name = percentileNames[percentile] || percentile;
                const value = (risk.currentValue * 1000).toFixed(1) + 'ms';
                const riskPercent = (risk.combinedRisk * 100).toFixed(1) + '%';
                
                html += `
                    <div class="percentile-card">
                        <div class="percentile-name">${name}</div>
                        <div class="percentile-value">${value}</div>
                        <div class="percentile-risk">风险: ${riskPercent}</div>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        // 更新业务影响
        function updateBusinessImpact(result) {
            if (result.businessImpact) {
                document.getElementById('userExperience').textContent = result.businessImpact.userExperience.level;
                document.getElementById('systemStability').textContent = result.businessImpact.systemStability.level;
                document.getElementById('businessContinuity').textContent = result.businessImpact.businessContinuity.level;
            }
        }

        // 更新告警
        function updateAlerts(result) {
            const container = document.getElementById('alertsContainer');
            
            if (!result.alerts || result.alerts.length === 0) {
                container.innerHTML = '<p style="color: #2ecc71; text-align: center; padding: 20px;">✅ 无告警，系统运行正常</p>';
                return;
            }

            let html = '';
            result.alerts.forEach(alert => {
                const alertClass = alert.level === 'CRITICAL' ? 'alert-critical' :
                                 alert.level === 'WARNING' ? 'alert-warning' : 'alert-info';
                
                html += `
                    <div class="alert ${alertClass}">
                        <strong>[${alert.level}]</strong> ${alert.message}
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        // 工具函数
        function formatTime(timestamp) {
            if (!timestamp) return '未知';
            const date = new Date(timestamp * 1000);
            return date.toLocaleTimeString();
        }

        function showToast(message, type = 'info', duration = 3000) {
            const existingToast = document.querySelector('.toast');
            if (existingToast) {
                existingToast.remove();
            }

            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.textContent = message;

            document.body.appendChild(toast);

            setTimeout(() => {
                if (toast.parentElement) {
                    toast.remove();
                }
            }, duration);
        }
    </script>
</body>
</html>
