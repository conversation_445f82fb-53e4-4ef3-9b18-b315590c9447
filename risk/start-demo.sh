#!/bin/bash

# 响应耗时风险检测算法可视化演示启动脚本

echo "🚀 启动响应耗时风险检测算法可视化演示"
echo "================================================"

# 检查当前目录
if [ ! -f "latency-risk-detector.js" ]; then
    echo "❌ 错误: 请在risk目录下运行此脚本"
    exit 1
fi

# 检查Python是否可用
if command -v python3 &> /dev/null; then
    echo "✅ 使用Python3启动HTTP服务器..."
    echo "📱 演示页面: http://localhost:8080/latency-demo.html"
    echo "🔧 完整页面: http://localhost:8080/latency-test.html"
    echo ""
    echo "按 Ctrl+C 停止服务器"
    echo "================================================"
    python3 -m http.server 8080
elif command -v python &> /dev/null; then
    echo "✅ 使用Python启动HTTP服务器..."
    echo "📱 演示页面: http://localhost:8080/latency-demo.html"
    echo "🔧 完整页面: http://localhost:8080/latency-test.html"
    echo ""
    echo "按 Ctrl+C 停止服务器"
    echo "================================================"
    python -m SimpleHTTPServer 8080
elif command -v npx &> /dev/null; then
    echo "✅ 使用Node.js启动HTTP服务器..."
    echo "📱 演示页面: http://localhost:8080/latency-demo.html"
    echo "🔧 完整页面: http://localhost:8080/latency-test.html"
    echo ""
    echo "按 Ctrl+C 停止服务器"
    echo "================================================"
    npx http-server -p 8080
else
    echo "❌ 错误: 未找到Python或Node.js"
    echo "请安装Python3或Node.js后重试"
    echo ""
    echo "或者直接在浏览器中打开HTML文件:"
    echo "📱 演示页面: $(pwd)/latency-demo.html"
    echo "🔧 完整页面: $(pwd)/latency-test.html"
    exit 1
fi
