<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SVG转PNG工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            max-width: 800px;
            width: 100%;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            font-weight: 700;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .upload-area {
            border: 3px dashed #667eea;
            border-radius: 15px;
            padding: 60px 20px;
            text-align: center;
            margin-bottom: 30px;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }

        .upload-area:hover {
            border-color: #764ba2;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.2), rgba(118, 75, 162, 0.2));
            transform: translateY(-2px);
        }

        .upload-area.dragover {
            border-color: #764ba2;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.3), rgba(118, 75, 162, 0.3));
        }

        .upload-icon {
            font-size: 4em;
            margin-bottom: 20px;
            color: #667eea;
        }

        .upload-text {
            font-size: 1.2em;
            color: #555;
            margin-bottom: 10px;
        }

        .upload-hint {
            font-size: 0.9em;
            color: #888;
        }

        #fileInput {
            display: none;
        }

        .controls {
            display: none;
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid #e9ecef;
        }

        .control-group {
            margin-bottom: 25px;
        }

        .control-group:last-child {
            margin-bottom: 0;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
            font-size: 1.1em;
        }

        input[type="number"], select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 1em;
            transition: all 0.3s ease;
            background: white;
        }

        input[type="number"]:focus, select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .size-controls {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .preview-area {
            display: none;
            text-align: center;
            margin-bottom: 30px;
        }

        .preview-container {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
            display: inline-block;
            max-width: 100%;
        }

        .preview-svg {
            max-width: 300px;
            max-height: 300px;
            border: 1px solid #ddd;
            border-radius: 10px;
            background:
                    linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
                    linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
                    linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
                    linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        }

        .convert-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 40px;
            font-size: 1.2em;
            font-weight: 600;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .convert-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
        }

        .convert-button:active {
            transform: translateY(-1px);
        }

        .convert-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            box-shadow: 0 5px 10px rgba(102, 126, 234, 0.2);
        }

        .download-area {
            display: none;
            text-align: center;
            padding: 30px;
            background: linear-gradient(135deg, rgba(34, 193, 195, 0.1), rgba(253, 187, 45, 0.1));
            border-radius: 15px;
            border: 2px solid #22c1c3;
            margin-top: 30px;
        }

        .download-button {
            background: linear-gradient(135deg, #22c1c3, #fdbb2d);
            color: white;
            text-decoration: none;
            padding: 15px 40px;
            font-size: 1.2em;
            font-weight: 600;
            border-radius: 50px;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 10px 20px rgba(34, 193, 195, 0.3);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .download-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(34, 193, 195, 0.4);
        }

        .success-message {
            margin-bottom: 20px;
            font-size: 1.1em;
            color: #28a745;
            font-weight: 600;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
            display: none;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }

            h1 {
                font-size: 2em;
            }

            .upload-area {
                padding: 40px 15px;
            }

            .size-controls {
                grid-template-columns: 1fr;
            }
        }

        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
<div class="container">
    <h1>🎨 SVG转PNG工具</h1>

    <div class="error-message" id="errorMessage"></div>

    <div class="upload-area" id="uploadArea">
        <div class="upload-icon">📁</div>
        <div class="upload-text">点击选择SVG文件或拖拽到此处</div>
        <div class="upload-hint">支持 .svg 格式文件</div>
        <input type="file" id="fileInput" accept=".svg,image/svg+xml">
    </div>

    <div class="controls" id="controls">
        <div class="control-group">
            <label for="quality">输出质量</label>
            <select id="quality">
                <option value="1">1x (原始尺寸)</option>
                <option value="2" selected>2x (高清)</option>
                <option value="3">3x (超高清)</option>
                <option value="4">4x (最高质量)</option>
            </select>
        </div>

        <div class="size-controls">
            <div class="control-group">
                <label for="customWidth">自定义宽度 (px)</label>
                <input type="number" id="customWidth" placeholder="留空使用原始宽度" min="1">
            </div>
            <div class="control-group">
                <label for="customHeight">自定义高度 (px)</label>
                <input type="number" id="customHeight" placeholder="留空使用原始高度" min="1">
            </div>
        </div>
    </div>

    <div class="preview-area" id="previewArea">
        <div class="preview-container">
            <div id="svgPreview"></div>
        </div>
        <button class="convert-button" id="convertButton">转换为PNG</button>
    </div>

    <div class="loading" id="loading">
        <div class="spinner"></div>
        <div>正在转换中...</div>
    </div>

    <div class="download-area" id="downloadArea">
        <div class="success-message">✅ 转换完成！背景已保持透明</div>
        <a href="#" class="download-button" id="downloadButton" download="converted.png">下载PNG文件</a>
    </div>
</div>

<script>
    class SVGToPNGConverter {
        constructor() {
            this.initializeElements();
            this.setupEventListeners();
            this.svgContent = null;
            this.originalWidth = 0;
            this.originalHeight = 0;
        }

        initializeElements() {
            this.uploadArea = document.getElementById('uploadArea');
            this.fileInput = document.getElementById('fileInput');
            this.controls = document.getElementById('controls');
            this.previewArea = document.getElementById('previewArea');
            this.svgPreview = document.getElementById('svgPreview');
            this.convertButton = document.getElementById('convertButton');
            this.downloadArea = document.getElementById('downloadArea');
            this.downloadButton = document.getElementById('downloadButton');
            this.errorMessage = document.getElementById('errorMessage');
            this.loading = document.getElementById('loading');
            this.qualitySelect = document.getElementById('quality');
            this.customWidth = document.getElementById('customWidth');
            this.customHeight = document.getElementById('customHeight');
        }

        setupEventListeners() {
            this.uploadArea.addEventListener('click', () => this.fileInput.click());
            this.fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
            this.convertButton.addEventListener('click', () => this.convertToPNG());

            // 拖拽功能
            this.uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                this.uploadArea.classList.add('dragover');
            });

            this.uploadArea.addEventListener('dragleave', () => {
                this.uploadArea.classList.remove('dragover');
            });

            this.uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                this.uploadArea.classList.remove('dragover');
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    this.handleFile(files[0]);
                }
            });
        }

        showError(message) {
            this.errorMessage.textContent = message;
            this.errorMessage.style.display = 'block';
            setTimeout(() => {
                this.errorMessage.style.display = 'none';
            }, 5000);
        }

        handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                this.handleFile(file);
            }
        }

        handleFile(file) {
            if (!file.type.includes('svg')) {
                this.showError('请选择SVG格式的文件！');
                return;
            }

            const reader = new FileReader();
            reader.onload = (e) => {
                this.svgContent = e.target.result;
                this.processSVG();
            };
            reader.onerror = () => {
                this.showError('文件读取失败，请重试！');
            };
            reader.readAsText(file);
        }

        processSVG() {
            try {
                // 创建临时SVG元素来获取尺寸
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = this.svgContent;
                const svgElement = tempDiv.querySelector('svg');

                if (!svgElement) {
                    throw new Error('无效的SVG文件');
                }

                // 获取SVG的原始尺寸
                const viewBox = svgElement.getAttribute('viewBox');
                let width = svgElement.getAttribute('width');
                let height = svgElement.getAttribute('height');

                if (viewBox) {
                    const viewBoxValues = viewBox.split(/\s+|,/);
                    if (viewBoxValues.length >= 4) {
                        this.originalWidth = parseFloat(viewBoxValues[2]);
                        this.originalHeight = parseFloat(viewBoxValues[3]);
                    }
                }

                if (width && height) {
                    this.originalWidth = parseFloat(width);
                    this.originalHeight = parseFloat(height);
                }

                if (this.originalWidth === 0 || this.originalHeight === 0) {
                    this.originalWidth = 300;
                    this.originalHeight = 300;
                }

                // 显示预览
                this.showPreview(svgElement.cloneNode(true));

            } catch (error) {
                this.showError('SVG文件解析失败：' + error.message);
            }
        }

        showPreview(svgElement) {
            // 设置预览SVG的样式
            svgElement.style.maxWidth = '300px';
            svgElement.style.maxHeight = '300px';
            svgElement.classList.add('preview-svg');

            this.svgPreview.innerHTML = '';
            this.svgPreview.appendChild(svgElement);

            // 显示控制面板和预览区域
            this.controls.style.display = 'block';
            this.previewArea.style.display = 'block';
            this.downloadArea.style.display = 'none';
        }

        async convertToPNG() {
            try {
                this.convertButton.disabled = true;
                this.loading.style.display = 'block';

                // 获取转换参数
                const quality = parseFloat(this.qualitySelect.value);
                const customW = this.customWidth.value ? parseInt(this.customWidth.value) : null;
                const customH = this.customHeight.value ? parseInt(this.customHeight.value) : null;

                // 计算最终尺寸
                let finalWidth, finalHeight;

                if (customW && customH) {
                    finalWidth = customW;
                    finalHeight = customH;
                } else if (customW) {
                    finalWidth = customW;
                    finalHeight = (customW / this.originalWidth) * this.originalHeight;
                } else if (customH) {
                    finalHeight = customH;
                    finalWidth = (customH / this.originalHeight) * this.originalWidth;
                } else {
                    finalWidth = this.originalWidth * quality;
                    finalHeight = this.originalHeight * quality;
                }

                // 创建canvas
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');

                canvas.width = finalWidth;
                canvas.height = finalHeight;

                // 创建图像
                const img = new Image();

                await new Promise((resolve, reject) => {
                    img.onload = () => {
                        // 清除画布并保持透明背景
                        ctx.clearRect(0, 0, canvas.width, canvas.height);

                        // 绘制图像
                        ctx.drawImage(img, 0, 0, finalWidth, finalHeight);
                        resolve();
                    };

                    img.onerror = () => reject(new Error('图像加载失败'));

                    // 创建SVG blob URL
                    const svgBlob = new Blob([this.svgContent], {type: 'image/svg+xml;charset=utf-8'});
                    const svgUrl = URL.createObjectURL(svgBlob);
                    img.src = svgUrl;
                });

                // 转换为PNG
                const pngDataUrl = canvas.toDataURL('image/png');

                // 设置下载链接
                this.downloadButton.href = pngDataUrl;
                this.downloadButton.download = `converted_${Date.now()}.png`;

                // 显示下载区域
                this.downloadArea.style.display = 'block';
                this.downloadArea.scrollIntoView({ behavior: 'smooth' });

            } catch (error) {
                this.showError('转换失败：' + error.message);
            } finally {
                this.convertButton.disabled = false;
                this.loading.style.display = 'none';
            }
        }
    }

    // 初始化应用
    document.addEventListener('DOMContentLoaded', () => {
        new SVGToPNGConverter();
    });
</script>
</body>
</html>